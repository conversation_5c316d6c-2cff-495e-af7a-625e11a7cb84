import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/models/todo.dart';
import '../../core/providers/todo_provider.dart';
import '../../core/models/category.dart' as cat;

class EditTodoDialog extends StatefulWidget {
  final Todo todo;

  const EditTodoDialog({
    super.key,
    required this.todo,
  });

  @override
  State<EditTodoDialog> createState() => _EditTodoDialogState();
}

class _EditTodoDialogState extends State<EditTodoDialog> {
  late TextEditingController _textController;
  late cat.Category? _selectedCategory;
  late String _selectedPriority;
  DateTime? _selectedDate;
  late List<Subtask> _subtasks;
  final TextEditingController _subtaskController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _textController = TextEditingController(text: widget.todo.text);
    final todoProvider = Provider.of<TodoProvider>(context, listen: false);
    final categories = todoProvider.categories;

    // Create a default category if none exist
    if (categories.isEmpty) {
      // Add a default category
      WidgetsBinding.instance.addPostFrameCallback((_) {
        todoProvider.addCategory('General');
      });
      _selectedCategory =
          cat.Category(id: '0', name: 'General', color: '#000000');
    } else {
      // Try to find the category by name
      try {
        _selectedCategory = categories.firstWhere(
          (c) => c.name == widget.todo.category,
          orElse: () => categories.first,
        );
      } catch (e) {
        // Fallback to first category or create a new one
        _selectedCategory = categories.isNotEmpty
            ? categories.first
            : cat.Category(id: '0', name: 'General', color: '#000000');
      }
    }

    _selectedPriority = widget.todo.priority;
    _selectedDate = widget.todo.dueDate;
    _subtasks = List.from(widget.todo.subtasks);
  }

  @override
  void dispose() {
    _textController.dispose();
    _subtaskController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final todoProvider = Provider.of<TodoProvider>(context);
    final categories = todoProvider.categories;
    final priorities = ['low', 'medium', 'high'];

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: 450,
        constraints: const BoxConstraints(maxHeight: 600),
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Padding(
              padding: EdgeInsets.only(bottom: 16),
              child: Text(
                'Edit Task',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            Flexible(
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TextField(
                      controller: _textController,
                      decoration: const InputDecoration(
                        labelText: 'Task',
                        hintText: 'Enter task description',
                        border: OutlineInputBorder(),
                      ),
                      autofocus: true,
                    ),
                    const SizedBox(height: 16),
                    if (categories.isEmpty)
                      // If no categories, show a disabled field with a message
                      TextFormField(
                        initialValue: 'No categories available',
                        enabled: false,
                        decoration: const InputDecoration(
                          labelText: 'Category',
                          border: OutlineInputBorder(),
                          contentPadding: EdgeInsets.symmetric(
                              horizontal: 12, vertical: 16),
                          suffixIcon: Icon(Icons.warning, color: Colors.orange),
                        ),
                      )
                    else
                      DropdownButtonFormField<cat.Category>(
                        value: _selectedCategory,
                        decoration: const InputDecoration(
                          labelText: 'Category',
                          border: OutlineInputBorder(),
                          contentPadding: EdgeInsets.symmetric(
                              horizontal: 12, vertical: 16),
                        ),
                        items: categories.map((category) {
                          return DropdownMenuItem(
                            value: category,
                            child: Text(category.name),
                          );
                        }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            setState(() => _selectedCategory = value);
                          }
                        },
                      ),
                    const SizedBox(height: 16),
                    DropdownButtonFormField<String>(
                      value: _selectedPriority,
                      decoration: const InputDecoration(
                        labelText: 'Priority',
                        border: OutlineInputBorder(),
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                      ),
                      items: priorities.map((priority) {
                        return DropdownMenuItem(
                          value: priority,
                          child: Text(priority.toUpperCase()),
                        );
                      }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() => _selectedPriority = value);
                        }
                      },
                    ),
                    const SizedBox(height: 16),
                    Container(
                      decoration: BoxDecoration(
                        border: Border.all(
                            color: Theme.of(context).colorScheme.outline),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: ListTile(
                        title: Text(
                          _selectedDate == null
                              ? 'No due date'
                              : 'Due: ${_selectedDate!.toString().split(' ')[0]}',
                          style: TextStyle(
                            color: Theme.of(context).colorScheme.onSurface,
                          ),
                        ),
                        trailing: IconButton(
                          icon: const Icon(Icons.calendar_today),
                          onPressed: () async {
                            final date = await showDatePicker(
                              context: context,
                              initialDate: _selectedDate ?? DateTime.now(),
                              firstDate: DateTime.now(),
                              lastDate:
                                  DateTime.now().add(const Duration(days: 365)),
                            );
                            if (date != null) {
                              setState(() => _selectedDate = date);
                            }
                          },
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    const Divider(),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          vertical: 8, horizontal: 12),
                      decoration: BoxDecoration(
                        color:
                            Theme.of(context).colorScheme.surfaceContainerLow,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              const Text(
                                'Subtasks',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                              ElevatedButton.icon(
                                onPressed: _addSubtask,
                                icon: const Icon(Icons.add, size: 18),
                                label: const Text('Add'),
                                style: ElevatedButton.styleFrom(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 12, vertical: 8),
                                  textStyle: const TextStyle(fontSize: 14),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          ..._buildSubtasksList(),
                          if (_subtasks.isEmpty)
                            const Padding(
                              padding: EdgeInsets.symmetric(vertical: 12.0),
                              child: Center(
                                child: Text(
                                  'No subtasks yet',
                                  style: TextStyle(
                                    fontStyle: FontStyle.italic,
                                    color: Colors.grey,
                                  ),
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                TextButton.icon(
                  onPressed: () => _deleteTodo(context, todoProvider),
                  icon: const Icon(Icons.delete, color: Colors.red),
                  label:
                      const Text('Delete', style: TextStyle(color: Colors.red)),
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                  ),
                ),
                Row(
                  children: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('Cancel'),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton(
                      onPressed: () async {
                        final newTitle = _textController.text.trim();

                        // Validate category
                        if (_selectedCategory == null && categories.isEmpty) {
                          // Create a default category if none exists
                          await todoProvider.addCategory('General');
                          _selectedCategory = cat.Category(
                              id: '0', name: 'General', color: '#000000');
                        }

                        // Create the updated todo with all properties
                        final updatedTodo = widget.todo.copyWith(
                          text: newTitle,
                          category: _selectedCategory?.name ?? 'General',
                          priority: _selectedPriority,
                          dueDate: _selectedDate,
                          subtasks: _subtasks,
                        );

                        try {
                          // Check if the title has changed
                          if (newTitle != widget.todo.text) {
                            debugPrint(
                                'Title changed from "${widget.todo.text}" to "$newTitle"');

                            // Validate the todo has required fields
                            if (widget.todo.id.isEmpty) {
                              throw Exception('Todo ID is missing');
                            }

                            if (widget.todo.priority.isEmpty) {
                              debugPrint(
                                  'Todo priority is empty, using default');
                            }

                            // Update the title first
                            await todoProvider.updateTodoTitle(
                                widget.todo.id, newTitle);
                          } else {
                            debugPrint('Title unchanged: "$newTitle"');
                          }

                          // Then update other properties if they've changed
                          if (updatedTodo.category != widget.todo.category ||
                              updatedTodo.priority != widget.todo.priority ||
                              updatedTodo.dueDate != widget.todo.dueDate) {
                            debugPrint(
                                'Other properties changed, updating todo');
                            // Use the updated todo with the new title
                            todoProvider.updateTodo(updatedTodo);
                          }

                          if (!context.mounted) return;
                          Navigator.pop(context); // Close dialog on success
                        } catch (e) {
                          // Show error message to user
                          if (!context.mounted) return;

                          // Extract the error message
                          String errorMessage = e.toString();
                          if (errorMessage.contains('Exception:')) {
                            errorMessage =
                                errorMessage.split('Exception:')[1].trim();
                          }

                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content:
                                  Text('Failed to update task: $errorMessage'),
                              backgroundColor:
                                  Theme.of(context).colorScheme.error,
                              duration: const Duration(seconds: 5),
                              action: SnackBarAction(
                                label: 'RETRY',
                                textColor:
                                    Theme.of(context).colorScheme.onError,
                                onPressed: () {
                                  // Close the snackbar
                                  ScaffoldMessenger.of(context)
                                      .hideCurrentSnackBar();
                                  // Try again
                                  Navigator.pop(context);
                                  showDialog(
                                    context: context,
                                    builder: (context) =>
                                        EditTodoDialog(todo: widget.todo),
                                  );
                                },
                              ),
                            ),
                          );
                        }
                      },
                      child: const Text('Save'),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _addSubtask() {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          width: 350,
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Padding(
                padding: EdgeInsets.only(bottom: 16),
                child: Text(
                  'Add Subtask',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              TextField(
                controller: _subtaskController,
                decoration: const InputDecoration(
                  labelText: 'Subtask',
                  hintText: 'Enter subtask description',
                  border: OutlineInputBorder(),
                ),
                autofocus: true,
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () {
                      Navigator.pop(context);
                      _subtaskController.clear();
                    },
                    child: const Text('Cancel'),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton(
                    onPressed: () async {
                      if (_subtaskController.text.trim().isNotEmpty) {
                        final todoProvider =
                            Provider.of<TodoProvider>(context, listen: false);
                        final newSubtask = Subtask(
                          text: _subtaskController.text.trim(),
                        );

                        // Add to local state for immediate UI update
                        setState(() {
                          _subtasks.add(newSubtask);
                        });

                        // Close dialog before async operation
                        Navigator.pop(context);
                        _subtaskController.clear();

                        // Sync with server
                        await todoProvider.addSubtask(
                            widget.todo.id, newSubtask);
                      }
                    },
                    child: const Text('Add'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _editSubtask(int index) {
    _subtaskController.text = _subtasks[index].text;
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          width: 350,
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Padding(
                padding: EdgeInsets.only(bottom: 16),
                child: Text(
                  'Edit Subtask',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              TextField(
                controller: _subtaskController,
                decoration: const InputDecoration(
                  labelText: 'Subtask',
                  hintText: 'Enter subtask description',
                  border: OutlineInputBorder(),
                ),
                autofocus: true,
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () {
                      Navigator.pop(context);
                      _subtaskController.clear();
                    },
                    child: const Text('Cancel'),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton(
                    onPressed: () async {
                      if (_subtaskController.text.trim().isNotEmpty) {
                        final todoProvider =
                            Provider.of<TodoProvider>(context, listen: false);
                        final updatedSubtask = _subtasks[index].copyWith(
                          text: _subtaskController.text.trim(),
                        );

                        // Update local state for immediate UI update
                        setState(() {
                          _subtasks[index] = updatedSubtask;
                        });

                        // Close dialog before async operation
                        Navigator.pop(context);
                        _subtaskController.clear();

                        // Sync with server
                        await todoProvider.updateSubtask(
                            widget.todo.id, updatedSubtask);
                      }
                    },
                    child: const Text('Save'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _toggleSubtaskComplete(int index) async {
    final todoProvider = Provider.of<TodoProvider>(context, listen: false);
    final updatedSubtask = _subtasks[index].copyWith(
      completed: !_subtasks[index].completed,
    );

    // Update local state for immediate UI update
    setState(() {
      _subtasks[index] = updatedSubtask;
    });

    // Sync with server
    await todoProvider.toggleSubtaskComplete(widget.todo.id, updatedSubtask.id);
  }

  Future<void> _deleteTodo(
      BuildContext context, TodoProvider todoProvider) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          width: 400,
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Delete Task',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'Are you sure you want to delete "${widget.todo.text}"?',
                style: TextStyle(
                  fontSize: 16,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.pop(context, false),
                    child: const Text('Cancel'),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton(
                    onPressed: () => Navigator.pop(context, true),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).colorScheme.error,
                      foregroundColor: Theme.of(context).colorScheme.onError,
                    ),
                    child: const Text('Delete'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );

    if (confirmed == true) {
      final todo = widget.todo; // Store for potential undo
      todoProvider.deleteTodo(widget.todo.id);

      if (!context.mounted) return;
      Navigator.pop(context); // Close the edit dialog

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Task deleted'),
          action: SnackBarAction(
            label: 'Undo',
            onPressed: () {
              todoProvider.addTodo(todo);
            },
          ),
        ),
      );
    }
  }

  Future<void> _deleteSubtask(int index) async {
    final todoProvider = Provider.of<TodoProvider>(context, listen: false);
    final subtaskId = _subtasks[index].id;

    // Update local state for immediate UI update
    setState(() {
      _subtasks.removeAt(index);
    });

    // Sync with server
    await todoProvider.deleteSubtask(widget.todo.id, subtaskId);
  }

  List<Widget> _buildSubtasksList() {
    return _subtasks.asMap().entries.map((entry) {
      final index = entry.key;
      final subtask = entry.value;
      return Container(
        margin: const EdgeInsets.only(bottom: 4),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: Theme.of(context).colorScheme.surfaceContainerLowest,
        ),
        child: ListTile(
          dense: true,
          contentPadding: const EdgeInsets.symmetric(horizontal: 8),
          leading: Checkbox(
            value: subtask.completed,
            onChanged: (_) => _toggleSubtaskComplete(index),
          ),
          title: Text(
            subtask.text,
            style: TextStyle(
              decoration: subtask.completed ? TextDecoration.lineThrough : null,
              color: subtask.completed
                  ? Theme.of(context).colorScheme.onSurface.withOpacity(0.5)
                  : Theme.of(context).colorScheme.onSurface,
            ),
          ),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                icon: const Icon(Icons.edit, size: 18),
                onPressed: () => _editSubtask(index),
                constraints: const BoxConstraints(
                  minWidth: 36,
                  minHeight: 36,
                ),
                padding: EdgeInsets.zero,
              ),
              IconButton(
                icon: const Icon(Icons.delete, size: 18),
                onPressed: () => _deleteSubtask(index),
                constraints: const BoxConstraints(
                  minWidth: 36,
                  minHeight: 36,
                ),
                padding: EdgeInsets.zero,
              ),
            ],
          ),
        ),
      );
    }).toList();
  }
}
