const { Pool } = require('pg');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const config = require('./configcred/configcred');

// PostgreSQL setup using config from configcred.js
const pool = new Pool(config.db);

const JWT_SECRET = config.secretKey; // Use the same secret key from config

function generateToken(user) {
    return jwt.sign({ id: user.id, email: user.email }, JWT_SECRET, { expiresIn: '7d' });
}

function authenticateToken(req, res, next) {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];
    if (!token) return res.status(401).json({ error: 'Authentication required' });
    jwt.verify(token, JWT_SECRET, (err, user) => {
        if (err) return res.status(403).json({ error: 'Invalid or expired token' });
        req.user = user;
        next();
    });
}

// Set up the routes for the Express app
function setupRoutes(app) {
    // Get all todo data for the logged-in user
    app.get('/api/todo/load', authenticateToken, async (req, res) => {
        try {
            const userId = req.user.id;
            const categories = (await pool.query('SELECT * FROM todo_categories WHERE user_id = $1', [userId])).rows;
            const tasks = (await pool.query('SELECT * FROM todo_items WHERE user_id = $1', [userId])).rows;
            
            // Fetch subtasks for all tasks
            const subtasks = (await pool.query(
                'SELECT * FROM todo_subtasks WHERE user_id = $1',
                [userId]
            )).rows;
            
            // Attach subtasks to their respective tasks
            tasks.forEach(task => {
                task.subtasks = subtasks.filter(subtask => subtask.todo_id === task.id);
            });
            
            // Fetch user name
            const userResult = await pool.query('SELECT id, email, name FROM todo_users WHERE id = $1', [userId]);
            const user = userResult.rows[0] || null;
            
            res.json({ categories, tasks, user });
        } catch (error) {
            console.error('Error in /api/todo/load:', error);
            res.status(500).json({ error: 'Server error', categories: [], tasks: [] });
        }
    });

    // Add a new category for the logged-in user
    app.post('/api/todo/categories', authenticateToken, async (req, res) => {
        const { name } = req.body;
        if (!name) return res.status(400).json({ error: 'Category name is required' });
        try {
            const userId = req.user.id;
            const result = await pool.query(
                'INSERT INTO todo_categories (name, user_id) VALUES ($1, $2) RETURNING *',
                [name, userId]
            );
            res.status(201).json(result.rows[0]);
        } catch (err) {
            res.status(500).json({ error: 'Failed to add category' });
        }
    });

    // Delete a category and its tasks for the logged-in user
    app.delete('/api/todo/categories/:id', authenticateToken, async (req, res) => {
        const { id } = req.params;
        const userId = req.user.id;
        try {
            await pool.query('DELETE FROM todo_items WHERE category_id = $1 AND user_id = $2', [id, userId]);
            await pool.query('DELETE FROM todo_categories WHERE id = $1 AND user_id = $2', [id, userId]);
            res.json({ success: true });
        } catch (err) {
            res.status(500).json({ error: 'Failed to delete category' });
        }
    });

    // Update a category for the logged-in user
    app.put('/api/todo/categories/:id', authenticateToken, async (req, res) => {
        const { id } = req.params;
        const { name } = req.body;
        if (!name) return res.status(400).json({ error: 'Category name is required' });
        const userId = req.user.id;
        try {
            const result = await pool.query(
                'UPDATE todo_categories SET name = $1 WHERE id = $2 AND user_id = $3 RETURNING *',
                [name, id, userId]
            );
            if (result.rows.length === 0) return res.status(404).json({ error: 'Category not found or not owned by user' });
            res.json(result.rows[0]);
        } catch (err) {
            console.error('Error in PUT /api/todo/categories/:id:', err);
            res.status(500).json({ error: 'Failed to update category', details: err.message });
        }
    });

    // Add a new task for the logged-in user
    app.post('/api/todo/tasks', authenticateToken, async (req, res) => {
        const { text, categoryId, priority, dueDate } = req.body;
        if (!text || !categoryId) return res.status(400).json({ error: 'Text and categoryId are required' });
        if (!['low', 'medium', 'high'].includes(priority)) return res.status(400).json({ error: 'Priority must be low, medium, or high' });
        try {
            const userId = req.user.id;
            // Check if category exists for this user
            const catRes = await pool.query('SELECT id FROM todo_categories WHERE id = $1 AND user_id = $2', [categoryId, userId]);
            if (catRes.rows.length === 0) {
                return res.status(400).json({ error: 'Invalid category for this user' });
            }
            const result = await pool.query(
                'INSERT INTO todo_items (id, text, completed, category_id, priority, due_date, user_id) VALUES ($1, $2, $3, $4, $5, $6, $7) RETURNING *',
                [Date.now(), text, false, categoryId, priority, dueDate || null, userId]
            );
            res.status(201).json(result.rows[0]);
        } catch (err) {
            console.error('Error in /api/todo/tasks:', err);
            res.status(500).json({ error: 'Failed to add task', details: err.message });
        }
    });

    // Update a task for the logged-in user
    app.put('/api/todo/tasks/:id', authenticateToken, async (req, res) => {
        const { id } = req.params;
        const { text, categoryId, priority, dueDate } = req.body;
        if (!text || !categoryId || !priority) return res.status(400).json({ error: 'Text, categoryId, and priority are required' });
        if (!['low', 'medium', 'high'].includes(priority)) return res.status(400).json({ error: 'Priority must be low, medium, or high' });
        const userId = req.user.id;
        try {
            // Check if category exists for this user
            const catRes = await pool.query('SELECT id FROM todo_categories WHERE id = $1 AND user_id = $2', [categoryId, userId]);
            if (catRes.rows.length === 0) {
                return res.status(400).json({ error: 'Invalid category for this user' });
            }
            const result = await pool.query(
                'UPDATE todo_items SET text = $1, category_id = $2, priority = $3, due_date = $4, updated_at = CURRENT_TIMESTAMP WHERE id = $5 AND user_id = $6 RETURNING *',
                [text, categoryId, priority, dueDate || null, id, userId]
            );
            if (result.rows.length === 0) return res.status(404).json({ error: 'Task not found' });
            res.json(result.rows[0]);
        } catch (err) {
            console.error('Error in PUT /api/todo/tasks/:id:', err);
            res.status(500).json({ error: 'Failed to update task', details: err.message });
        }
    });

    // Toggle task completion status for the logged-in user
    app.patch('/api/todo/tasks/:id/toggle', authenticateToken, async (req, res) => {
        const { id } = req.params;
        const userId = req.user.id;
        try {
            const result = await pool.query(
                'UPDATE todo_items SET completed = NOT completed, updated_at = CURRENT_TIMESTAMP WHERE id = $1 AND user_id = $2 RETURNING *',
                [id, userId]
            );
            if (result.rows.length === 0) return res.status(404).json({ error: 'Task not found' });
            res.json(result.rows[0]);
        } catch (err) {
            res.status(500).json({ error: 'Failed to toggle task' });
        }
    });

    // Delete a task for the logged-in user
    app.delete('/api/todo/tasks/:id', authenticateToken, async (req, res) => {
        const { id } = req.params;
        const userId = req.user.id;
        try {
            await pool.query('DELETE FROM todo_items WHERE id = $1 AND user_id = $2', [id, userId]);
            res.json({ success: true });
        } catch (err) {
            res.status(500).json({ error: 'Failed to delete task' });
        }
    });

    // Reorder tasks (update display_order for each task)
    app.post('/api/todo/tasks/reorder', authenticateToken, async (req, res) => {
        const { order } = req.body; // [{id, display_order}, ...]
        if (!Array.isArray(order)) return res.status(400).json({ error: 'Order must be an array' });
        const userId = req.user.id;
        const client = await pool.connect();
        try {
            await client.query('BEGIN');
            for (const { id, display_order } of order) {
                await client.query(
                    'UPDATE todo_items SET display_order = $1 WHERE id = $2 AND user_id = $3',
                    [display_order, id, userId]
                );
            }
            await client.query('COMMIT');
            res.json({ success: true });
        } catch (err) {
            await client.query('ROLLBACK');
            console.error('Error in /api/todo/tasks/reorder:', err);
            res.status(500).json({ error: 'Failed to reorder tasks', details: err.message });
        } finally {
            client.release();
        }
    });

    // User registration
    app.post('/api/todo/register', async (req, res) => {
        const { email, password, name } = req.body;
        if (!email || !password) return res.status(400).json({ error: 'Email and password required' });
        try {
            const hashed = await bcrypt.hash(password, 10);
            const result = await pool.query(
                'INSERT INTO todo_users (email, password_hash, name) VALUES ($1, $2, $3) RETURNING id, email, name',
                [email, hashed, name || null]
            );
            const user = result.rows[0];
            const token = generateToken(user);
            res.status(201).json({ token, user });
        } catch (err) {
            if (err.code === '23505') return res.status(400).json({ error: 'Email already registered' });
            res.status(500).json({ error: 'Registration failed' });
        }
    });

    // User login
    app.post('/api/todo/login', async (req, res) => {
        const { email, password } = req.body;
        if (!email || !password) return res.status(400).json({ error: 'Email and password required' });
        try {
            const result = await pool.query('SELECT * FROM todo_users WHERE email = $1', [email]);
            if (result.rows.length === 0) return res.status(401).json({ error: 'Invalid credentials' });
            const user = result.rows[0];
            const valid = await bcrypt.compare(password, user.password_hash);
            if (!valid) return res.status(401).json({ error: 'Invalid credentials' });
            const token = generateToken(user);
            res.json({ token, user: { id: user.id, email: user.email, name: user.name } });
        } catch (err) {
            res.status(500).json({ error: 'Login failed' });
        }
    });

    // Forgot password (dummy, just checks if email exists)
    app.post('/api/todo/forgot-password', async (req, res) => {
        const { email } = req.body;
        if (!email) return res.status(400).json({ error: 'Email required' });
        try {
            const result = await pool.query('SELECT id FROM todo_users WHERE email = $1', [email]);
            if (result.rows.length === 0) return res.status(404).json({ error: 'Email not found' });
            // In production, send a reset email here
            res.json({ message: 'If this email exists, a reset link will be sent.' });
        } catch (err) {
            res.status(500).json({ error: 'Failed to process request' });
        }
    });

    // Add a new subtask
    app.post('/api/todo/tasks/:taskId/subtasks', authenticateToken, async (req, res) => {
        const { taskId } = req.params;
        const { text } = req.body;
        const userId = req.user.id;
        
        if (!text) return res.status(400).json({ error: 'Subtask text is required' });
        
        try {
            // Verify task ownership
            const taskCheck = await pool.query(
                'SELECT id FROM todo_items WHERE id = $1 AND user_id = $2',
                [taskId, userId]
            );
            if (taskCheck.rows.length === 0) {
                return res.status(404).json({ error: 'Task not found or not owned by user' });
            }

            const result = await pool.query(
                'INSERT INTO todo_subtasks (todo_id, text, completed, user_id) VALUES ($1, $2, $3, $4) RETURNING *',
                [taskId, text, false, userId]
            );
            res.status(201).json(result.rows[0]);
        } catch (err) {
            console.error('Error in POST /api/todo/tasks/:taskId/subtasks:', err);
            res.status(500).json({ error: 'Failed to add subtask', details: err.message });
        }
    });

    // Update a subtask
    app.put('/api/todo/subtasks/:id', authenticateToken, async (req, res) => {
        const { id } = req.params;
        const { text, completed } = req.body;
        const userId = req.user.id;
        
        if (!text) return res.status(400).json({ error: 'Subtask text is required' });
        
        try {
            const result = await pool.query(
                'UPDATE todo_subtasks SET text = $1, completed = $2, updated_at = CURRENT_TIMESTAMP WHERE id = $3 AND user_id = $4 RETURNING *',
                [text, completed, id, userId]
            );
            if (result.rows.length === 0) {
                return res.status(404).json({ error: 'Subtask not found or not owned by user' });
            }
            res.json(result.rows[0]);
        } catch (err) {
            console.error('Error in PUT /api/todo/subtasks/:id:', err);
            res.status(500).json({ error: 'Failed to update subtask', details: err.message });
        }
    });

    // Delete a subtask
    app.delete('/api/todo/subtasks/:id', authenticateToken, async (req, res) => {
        const { id } = req.params;
        const userId = req.user.id;
        
        try {
            const result = await pool.query(
                'DELETE FROM todo_subtasks WHERE id = $1 AND user_id = $2 RETURNING *',
                [id, userId]
            );
            if (result.rows.length === 0) {
                return res.status(404).json({ error: 'Subtask not found or not owned by user' });
            }
            res.json({ success: true });
        } catch (err) {
            console.error('Error in DELETE /api/todo/subtasks/:id:', err);
            res.status(500).json({ error: 'Failed to delete subtask', details: err.message });
        }
    });
}

module.exports = {
    setupRoutes
};