<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Todo App</title>
  
  <!-- Critical resources with preload -->
  <link rel="preload" href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" as="style">
  <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" as="style" 
    integrity="sha512-9usAa10IRO0HhonpyAIVpjrylPvoDwiPUiKdWk5t3PyolY1cOd4DSE0Ga+ri4AuTroPR5aQvXU9xC6qOPnzFeg=="
    crossorigin="anonymous">
  <link rel="preload" href="https://cdn.jsdelivr.net/npm/shepherd.js@9.1.1/dist/css/shepherd.css" as="style">
  <link rel="preload" href="https://cdn.jsdelivr.net/npm/sweetalert2@11" as="script">
  <link rel="preload" href="https://cdn.jsdelivr.net/npm/shepherd.js@9.1.1/dist/js/shepherd.min.js" as="script">
  <link rel="preload" href="https://unpkg.com/vue@3/dist/vue.global.prod.js" as="script">
  
  <!-- Load resources -->
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
    integrity="sha512-9usAa10IRO0HhonpyAIVpjrylPvoDwiPUiKdWk5t3PyolY1cOd4DSE0Ga+ri4AuTroPR5aQvXU9xC6qOPnzFeg=="
    crossorigin="anonymous" referrerpolicy="no-referrer" />
  <link rel="icon" type="image/png" href="/images/favicon.png">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/shepherd.js@9.1.1/dist/css/shepherd.css">

  <style>
    /* Loading screen styles - placed before other styles for immediate rendering */
    #app-loader {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #f9fafb;
      z-index: 9999;
      transition: opacity 0.5s ease-out, visibility 0.5s ease-out;
    }

    html {
      text-size-adjust: 100%;
    }

    .dark #app-loader {
      background-color: #1a202c;
    }

    .loader-content {
      text-align: center;
      transform: translateY(-20px);
    }

    .loader-logo {
      width: 64px;
      height: 64px;
      margin: 0 auto 1rem;
      animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    }

    .loader-spinner {
      width: 40px;
      height: 40px;
      margin: 1rem auto;
      border: 3px solid #e5e7eb;
      border-radius: 50%;
      border-top-color: #3b82f6;
      animation: spin 1s linear infinite;
      will-change: transform;
    }

    .dark .loader-spinner {
      border-color: #374151;
      border-top-color: #60a5fa;
    }

    .loader-text {
      color: #4b5563;
      font-size: 1.125rem;
      font-weight: 500;
      margin-top: 1rem;
      opacity: 0;
      animation: fadeIn 0.5s ease-out forwards 0.3s;
    }

    .dark .loader-text {
      color: #e5e7eb;
    }

    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.5; }
    }

    @keyframes spin {
      to { transform: rotate(360deg); }
    }

    @keyframes fadeIn {
      to { opacity: 1; }
    }

    /* Hide loader when app is ready */
    #app-loader.hidden {
      opacity: 0;
      visibility: hidden;
    }

    /* --- Tailwind CSS and Utility Overrides --- */
    .priority-high {
      border-left: 4px solid #ef4444;
    }
    .priority-medium {
      border-left: 4px solid #f59e0b;
    }

    .priority-low {
      border-left: 4px solid #10b981;
    }

    .progress-bar {
      height: 4px;
      background-color: #e5e7eb;
      border-radius: 2px;
      margin-top: 8px;
    }

    .progress-fill {
      height: 100%;
      background-color: #3b82f6;
      border-radius: 2px;
      transition: width 0.3s ease;
    }

    body.dark {
      background-color: #1a202c !important;
      color: #f3f4f6 !important;
    }

    .dark .bg-white {
      background-color: #23272f !important;
      color: #f3f4f6 !important;
    }

    .dark .text-gray-800,
    .dark .text-gray-700 {
      color: #f3f4f6 !important;
    }

    .dark .text-gray-500 {
      color: #a0aec0 !important;
    }

    .dark .bg-gray-100 {
      background-color: #23272f !important;
    }

    .dark .bg-gray-200 {
      background-color: #2d3748 !important;
    }

    .dark .bg-gray-300 {
      background-color: #4a5568 !important;
    }

    .dark .bg-blue-100 {
      background-color: #243c5a !important;
    }

    .dark .bg-yellow-100 {
      background-color: #5a4c24 !important;
    }

    .dark .bg-red-100 {
      background-color: #5a2424 !important;
    }

    .dark .bg-green-100 {
      background-color: #245a3c !important;
    }

    .dark .shadow {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.6) !important;
    }

    .dark .todo-card {
      background-color: #23272f !important;
      color: #f3f4f6 !important;
    }

    .dark input[type="text"],
    .dark input[type="date"],
    .dark select {
      background-color: #23272f !important;
      color: #f3f4f6 !important;
      border-color: #4a5568 !important;
    }

    .dark input[type="text"]::placeholder {
      color: #a0aec0 !important;
    }

    .dark .bg-red-100,
    .dark .text-red-800 {
      background-color: #5a2424 !important;
      color: #fca5a5 !important;
    }

    .dark .bg-yellow-100,
    .dark .text-yellow-800 {
      background-color: #5a4c24 !important;
      color: #fde68a !important;
    }

    .dark .bg-green-100,
    .dark .text-green-800 {
      background-color: #245a3c !important;
      color: #6ee7b7 !important;
    }

    .transition-bg {
      transition: background-color 0.5s, color 0.5s;
    }

    .swal2-dark {
      background: #23272f !important;
      color: #f3f4f6 !important;
    }

    .swal2-dark .swal2-title,
    .swal2-dark .swal2-html-container,
    .swal2-dark .swal2-content {
      color: #f3f4f6 !important;
    }

    .swal2-dark .swal2-confirm {
      background-color: #2563eb !important;
      color: #fff !important;
    }

    .swal2-dark .swal2-cancel {
      background-color: #374151 !important;
      color: #f3f4f6 !important;
    }

    .swal2-dark .swal2-popup {
      box-shadow: 0 4px 12px rgba(0,0,0,0.8) !important;
    }

    /* Custom toggle switch styles */
    .toggle-switch {
      position: relative;
      display: inline-block;
      width: 48px;
      height: 24px;
      flex-shrink: 0;
    }

    .toggle-switch input {
      opacity: 0;
      width: 0;
      height: 0;
    }

    .toggle-switch .slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #e5e7eb;
      transition: .3s;
      border-radius: 24px;
    }

    .dark .toggle-switch .slider {
      background-color: #4b5563;
    }

    .toggle-switch .slider:before {
      position: absolute;
      content: "";
      height: 18px;
      width: 18px;
      left: 3px;
      bottom: 3px;
      background-color: white;
      transition: .3s;
      border-radius: 50%;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .toggle-switch input:checked + .slider {
      background-color: #10b981;
    }

    .toggle-switch input:checked + .slider:before {
      transform: translateX(24px);
    }

    .toggle-switch input:focus + .slider {
      box-shadow: 0 0 1px #10b981;
    }

    .toggle-switch:hover .slider:before {
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    /* Add transition for text color when completed */
    .subtask-text {
      transition: color 0.3s ease;
    }

    .subtask-text.completed {
      color: #9ca3af;
      text-decoration: line-through;
    }

    /* Custom transition and animation styles */
    .list-none, .flex-row {
      position: relative;
      min-height: 200px;
    }
    .todo-card {
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      transform: translateZ(0);
      will-change: transform, opacity;
      position: relative;
      margin-bottom: 1rem;
    }
    .todo-card.dragging {
      cursor: grabbing;
      opacity: 0.9;
      transform: scale(1.02) rotate(-1deg) translateY(-4px);
      z-index: 20;
      box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }
    .fade-move, .slide-move {
      transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    }
    .fade-enter-active, .fade-leave-active,
    .slide-enter-active, .slide-leave-active {
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }
    .fade-enter-from, .slide-enter-from {
      opacity: 0;
      transform: translateY(30px) scale(0.95);
    }
    .fade-leave-to, .slide-leave-to {
      opacity: 0;
      transform: translateY(-30px) scale(0.95);
      position: absolute;
    }
    .fade-leave-active, .slide-leave-active {
      position: absolute;
      width: calc(100% - 2rem);
    }
    .transform {
      transform-style: preserve-3d;
      backface-visibility: hidden;
    }
    .origin-bottom {
      transform-origin: bottom center;
    }

    /* Modal Pop Animation */
    @keyframes modal-pop-in {
      0% { transform: scale(0.7); opacity: 0; }
      45% { transform: scale(1.05); opacity: 0.7; }
      80% { transform: scale(0.95); opacity: 0.9; }
      100% { transform: scale(1); opacity: 1; }
    }

    @keyframes modal-pop-out {
      0% { transform: scale(1); opacity: 1; }
      100% { transform: scale(0.7); opacity: 0; }
    }

    .modal-pop-enter-active {
      animation: modal-pop-in 0.3s ease-out;
    }

    .modal-pop-leave-active {
      animation: modal-pop-out 0.3s ease-in;
    }
    /* End Modal Pop Animation */

    /* Dropdown menu dark mode hover fix */
    .dark .dropdown-menu-item:hover {
      background-color: #374151 !important; /* Tailwind's bg-gray-600 */
    }

    /* Shepherd.js custom theme for better contrast and focus */
    .shepherd-element {
      border-radius: 12px !important;
      box-shadow: 0 8px 32px 0 rgba(0,0,0,0.25), 0 1.5px 8px 0 rgba(0,0,0,0.10) !important;
      border: none !important;
      font-family: inherit;
    }
    .shepherd-element .shepherd-header {
      background: transparent !important;
      border-bottom: none !important;
      color: inherit;
    }
    .shepherd-element .shepherd-title {
      font-size: 1.25rem;
      font-weight: bold;
      color: #2563eb;
    }
    .dark .shepherd-element .shepherd-title {
      color: #60a5fa;
    }
    .shepherd-element .shepherd-text {
      color: #23272f;
      font-size: 1rem;
    }
    .dark .shepherd-element .shepherd-text {
      color: #f3f4f6;
    }
    .shepherd-element .shepherd-footer {
      background: transparent !important;
      border-top: none !important;
      display: flex;
      gap: 0.5rem;
      justify-content: flex-end;
    }
    .shepherd-element .shepherd-button {
      background: #2563eb !important;
      color: #fff !important;
      border-radius: 8px !important;
      font-weight: 600;
      padding: 0.5rem 1.2rem !important;
      border: none !important;
      box-shadow: 0 2px 8px rgba(37,99,235,0.08);
      transition: background 0.2s;
    }
    .shepherd-element .shepherd-button:not(:disabled):hover {
      background: #1d4ed8 !important;
    }
    .dark .shepherd-element .shepherd-button {
      background: #60a5fa !important;
      color: #23272f !important;
    }
    .dark .shepherd-element .shepherd-button:not(:disabled):hover {
      background: #2563eb !important;
      color: #fff !important;
    }
    .shepherd-modal-overlay-container {
      z-index: 10050 !important;
    }
    .shepherd-modal-overlay-container .shepherd-modal-overlay {
      background: rgba(30,41,59,0.55) !important;
      -webkit-backdrop-filter: blur(2px);
      backdrop-filter: blur(2px);
      transition: background 0.3s;
    }
    .dark .shepherd-modal-overlay-container .shepherd-modal-overlay {
      background: rgba(16,23,42,0.85) !important;
    }
    /* Focus ring for highlighted elements */
    .shepherd-enabled.shepherd-target {
      box-shadow: 0 0 0 4px #2563eb, 0 8px 32px 0 rgba(0,0,0,0.18) !important;
      border-radius: 10px !important;
      z-index: 10060 !important;
      transition: box-shadow 0.2s;
    }
    .dark .shepherd-enabled.shepherd-target {
      box-shadow: 0 0 0 4px #60a5fa, 0 8px 32px 0 rgba(0,0,0,0.28) !important;
    }
  </style>
  <script>
    // Set dark mode class before Vue loads to prevent flash
    (function () {
      try {
        var dark = JSON.parse(localStorage.getItem('darkMode'));
        if (dark) {
          document.documentElement.classList.add('dark');
          document.body.classList.add('dark', 'transition-bg');
          document.body.classList.remove('bg-gray-100', 'text-gray-800');
        }
      } catch (e) { }
    })();
  </script>
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <script src="https://cdn.jsdelivr.net/npm/shepherd.js@9.1.1/dist/js/shepherd.min.js"></script>
  <script>
    function startTodoTutorialImpl() {
      if (window.todoShepherd) { window.todoShepherd.cancel(); }
      const tour = new Shepherd.Tour({
        defaultStepOptions: {
          classes: 'shadow-lg bg-white dark:bg-gray-800',
          scrollTo: { behavior: 'smooth', block: 'center' },
          cancelIcon: { enabled: true }
        }
      });
      window.todoShepherd = tour;
      tour.addStep({
        title: 'Welcome to Todo App!',
        text: 'Let’s take a quick tour of the main features. You can skip or replay this tutorial anytime.',
        buttons: [
          { text: 'Skip', action: tour.cancel },
          { text: 'Start', action: tour.next }
        ]
      });
      tour.addStep({
        title: 'Login or Register',
        text: 'Start by creating an account or logging in to sync your tasks.',
        attachTo: { element: 'button[aria-label="Login"], button:has(.fa-user)', on: 'bottom' },
        buttons: [{ text: 'Next', action: tour.next }]
      });
      tour.addStep({
        title: 'Theme Switch',
        text: 'Toggle between light and dark mode for your comfort.',
        attachTo: { element: '#settings-button', on: 'left' },
        buttons: [{ text: 'Next', action: tour.next }]
      });
      tour.addStep({
        title: 'Categories',
        text: 'Organize your tasks by categories. Add, edit, or delete categories here.',
        attachTo: { element: 'button[title="Add Category"]', on: 'bottom' },
        buttons: [{ text: 'Next', action: tour.next }]
      });
      tour.addStep({
        title: 'Add a Task',
        text: 'Add your first task. You can set its priority and due date.',
        attachTo: { element: 'button[title="Add Task"]', on: 'bottom' },
        buttons: [{ text: 'Next', action: tour.next }]
      });
      tour.addStep({
        title: 'Task Actions',
        text: 'Edit, complete, or delete tasks using these buttons.',
        attachTo: { element: '.todo-card button', on: 'left' },
        buttons: [{ text: 'Next', action: tour.next }]
      });
      tour.addStep({
        title: 'Subtasks',
        text: 'Break tasks into smaller steps with subtasks (edit a task to see this).',
        attachTo: { element: '.todo-card', on: 'top' },
        buttons: [{ text: 'Next', action: tour.next }]
      });
      tour.addStep({
        title: 'Search & Filter',
        text: 'Quickly find tasks or filter by category.',
        attachTo: { element: 'input[placeholder="Search tasks..."]', on: 'bottom' },
        buttons: [{ text: 'Next', action: tour.next }]
      });
      tour.addStep({
        title: 'Settings Menu',
        text: 'Access more options like marking all complete, changing view, and toggling dark mode.',
        attachTo: { element: '#settings-button', on: 'left' },
        buttons: [{ text: 'Next', action: tour.next }]
      });
      tour.addStep({
        title: 'Drag & Drop',
        text: 'Reorder tasks by dragging and dropping them.',
        attachTo: { element: '.todo-card', on: 'top' },
        buttons: [{ text: 'Finish', action: tour.complete }]
      });
      tour.on('complete', () => localStorage.setItem('todo_tutorial_seen', '1'));
      tour.on('cancel', () => localStorage.setItem('todo_tutorial_seen', '1'));
      tour.start();
    }
    // Ensure the function is available globally
    window.startTodoTutorialImpl = startTodoTutorialImpl;
    // On first load, run tutorial if not seen
    document.addEventListener('DOMContentLoaded', function() {
      if (!localStorage.getItem('todo_tutorial_seen')) {
        startTodoTutorialImpl();
      }
    });
  </script>
</head>

<body :class="['font-sans antialiased', darkMode ? 'dark transition-bg' : 'transition-bg']">
  <!-- Loading screen -->
  <div id="app-loader">
    <div class="loader-content">
      <img src="images/favicon.png" alt="Todo App Logo" class="loader-logo">
      <div class="loader-spinner"></div>
      <p class="loader-text">Loading Todo App...</p>
    </div>
  </div>

  <div id="app">
    <!-- Auth Modals -->
    <transition name="modal-pop">
      <div v-if="showAuthModal && !isAuthenticated"
        class="fixed inset-0 flex items-center justify-center bg-gray-900 bg-opacity-50 dark:bg-opacity-70 backdrop-blur-sm z-50 transition-all duration-300">
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg dark:shadow-2xl p-8 w-full max-w-sm relative"> <!-- Removed transform classes -->
          <!-- Add close button -->
          <button @click="showAuthModal = false"
            class="absolute top-4 right-4 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 transition-colors duration-200" title="Close">
          <i class="fas fa-times text-xl"></i>
        </button>
        
        <h2 class="text-2xl font-bold mb-6 text-center text-gray-800 dark:text-gray-100">{{ authMode === 'login' ? 'Welcome Back' : authMode === 'register' ? 'Create Account' : 'Reset Password' }}</h2>
        
        <form v-if="authMode === 'login'" class="space-y-4" @submit.prevent="login">
          <div class="space-y-2">
            <input v-model="loginEmail" type="email" placeholder="Email" autocomplete="username"
              class="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 
              bg-white dark:bg-gray-700 
              text-gray-900 dark:text-gray-100 
              placeholder-gray-500 dark:placeholder-gray-400
              focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent
              transition-colors duration-200">
            <input v-model="loginPassword" type="password" placeholder="Password" autocomplete="current-password"
              class="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 
              bg-white dark:bg-gray-700 
              text-gray-900 dark:text-gray-100 
              placeholder-gray-500 dark:placeholder-gray-400
              focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent
              transition-colors duration-200">
          </div>
          <button @click="login" 
            class="w-full bg-blue-500 hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700 
            text-white font-bold py-3 px-4 rounded-lg transition-colors duration-200
            focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800" title="Login">
            Login
          </button>
          <div class="flex flex-col space-y-2">
            <button @click="authMode = 'register'" 
              class="w-full text-blue-500 dark:text-blue-400 hover:text-blue-600 dark:hover:text-blue-300 
              font-medium transition-colors duration-200" title="Create Account">
              Create Account
            </button>
            <button @click="authMode = 'forgot'" 
              class="w-full text-gray-500 dark:text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 
              text-sm transition-colors duration-200" title="Forgot Password">
              Forgot Password?
            </button>
          </div>
        </form>

        <form v-else-if="authMode === 'register'" class="space-y-4" @submit.prevent="register">
          <div class="space-y-2">
            <input v-model="registerName" type="text" placeholder="Name (optional)" autocomplete="name"
              class="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 
              bg-white dark:bg-gray-700 
              text-gray-900 dark:text-gray-100 
              placeholder-gray-500 dark:placeholder-gray-400
              focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent
              transition-colors duration-200">
            <input v-model="registerEmail" type="email" placeholder="Email" autocomplete="email"
              class="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 
              bg-white dark:bg-gray-700 
              text-gray-900 dark:text-gray-100 
              placeholder-gray-500 dark:placeholder-gray-400
              focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent
              transition-colors duration-200">
            <input v-model="registerPassword" type="password" placeholder="Password" autocomplete="new-password"
              class="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 
              bg-white dark:bg-gray-700 
              text-gray-900 dark:text-gray-100 
              placeholder-gray-500 dark:placeholder-gray-400
              focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent
              transition-colors duration-200">
          </div>
          <button @click="register" 
            class="w-full bg-green-500 hover:bg-green-600 dark:bg-green-600 dark:hover:bg-green-700 
            text-white font-bold py-3 px-4 rounded-lg transition-colors duration-200
            focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800">
            Create Account
          </button>
          <button @click="authMode = 'login'" 
            class="w-full text-blue-500 dark:text-blue-400 hover:text-blue-600 dark:hover:text-blue-300 
            font-medium transition-colors duration-200">
            Back to Login
          </button>
        </form>

        <div v-else-if="authMode === 'forgot'" class="space-y-4">
          <div class="space-y-2">
            <input v-model="forgotEmail" type="email" placeholder="Email" 
              class="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 
              bg-white dark:bg-gray-700 
              text-gray-900 dark:text-gray-100 
              placeholder-gray-500 dark:placeholder-gray-400
              focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent
              transition-colors duration-200">
          </div>
          <button @click="forgotPassword" 
            class="w-full bg-yellow-500 hover:bg-yellow-600 dark:bg-yellow-600 dark:hover:bg-yellow-700 
            text-white font-bold py-3 px-4 rounded-lg transition-colors duration-200
            focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800" title="Send Reset Link">
            Send Reset Link
          </button>
          <button @click="authMode = 'login'" 
            class="w-full text-blue-500 dark:text-blue-400 hover:text-blue-600 dark:hover:text-blue-300 
            font-medium transition-colors duration-200" title="Back to Login">
            Back to Login
          </button>
        </div>

        <div v-if="authError" 
          class="mt-4 p-3 rounded-lg bg-red-100 dark:bg-red-900/50 text-red-700 dark:text-red-200 text-sm text-center">
          {{ authError }}
        </div>
      </div>
    </div>
    </transition>
    <!-- Navbar -->
    <div class="container mx-auto p-4">
      <nav class="flex flex-col sm:flex-row sm:items-center sm:justify-between bg-transparent rounded-xl px-4 py-3 mb-6">
        <a href="/todoapp" class="flex items-center font-bold text-lg">
          <img src="images/favicon.png" alt="App Icon" class="w-7 h-7 mr-2 inline-block align-middle"> Todo App <span class="ml-2 text-xs font-semibold text-gray-500 dark:text-gray-400 bg-gray-200 dark:bg-gray-700 px-1.5 py-0.5 rounded-full">Beta</span>
        </a>
        <div class="flex flex-wrap gap-2 mb-2 sm:mb-0">
          <button @click="selectedCategory = null"
            :class="['px-3 py-1 rounded-xl font-semibold text-sm', !selectedCategory ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-blue-100']"
            title="Show all tasks">
            All
          </button>
          <div v-for="category in categories" :key="category.id" class="relative group inline-block">
            <div class="absolute left-1/2 -translate-x-1/2 -top-10 flex gap-2 opacity-0 group-hover:opacity-100 transform origin-bottom scale-75 group-hover:scale-100 -translate-y-0 group-hover:-translate-y-1 transition-all duration-200 z-10">
              <button @click.stop="handleEditCategory(category)" class="text-blue-500 hover:text-blue-700 p-1 hover:scale-110 transition-transform duration-200" title="Edit Category">
                <i class="fas fa-edit"></i>
              </button>
              <button @click.stop="handleDeleteCategory(category)" class="text-red-500 hover:text-red-700 p-1 hover:scale-110 transition-transform duration-200" title="Delete Category">
                <i class="fas fa-trash"></i>
              </button>
            </div>
            <button @click="selectedCategory = selectedCategory === category.name ? null : category.name"
              :class="['px-3 py-1 rounded-xl font-semibold text-sm', selectedCategory === category.name ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-blue-100']"
              :title="'Filter tasks by ' + category.name">
              {{ category.name }}
            </button>
          </div>
          <button @click="showAddCategoryModal = true" class="px-3 py-1 rounded-xl font-semibold text-sm bg-blue-500 text-white hover:bg-green-600 flex items-center" title="Add New Category">
            <i class="fas fa-plus"></i>
          </button>
        </div>
        <div class="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-2">
          <input type="text" v-model="searchQuery" placeholder="Search tasks..."
            class="shadow appearance-none border rounded-xl py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline flex-1"
            title="Search tasks by name or category">
          <!-- Add Task Button (only if authenticated) -->
            <button @click="showAddModal = true" :disabled="!canAddTodo" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-xl focus:outline-none focus:shadow-outline flex-shrink-0 disabled:opacity-50 disabled:cursor-not-allowed" title="Add New Task">
              <i class="fas fa-plus"></i>
            </button>
          
          
          <!-- Settings Dropdown -->
          <div class="relative inline-block text-left" >
            <button @click="showSettingsMenu = !showSettingsMenu" type="button" id="settings-button" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-xl focus:outline-none focus:shadow-outline flex items-center justify-center" title="Open Settings Menu">
              <i class="fas fa-cog"></i>
            </button>

            <!-- Dropdown panel -->
            <div v-if="showSettingsMenu" ref="settingsMenuRef"
                 class="origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white dark:bg-gray-700 ring-1 ring-black ring-opacity-5 focus:outline-none z-20">
              <div class="py-1" role="menu" aria-orientation="vertical" aria-labelledby="options-menu">
                <!-- Mark All Complete (only if authenticated) -->
                <template v-if="isAuthenticated">
                  <button @click="markAllComplete(); showSettingsMenu = false" class="dropdown-menu-item w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 !dark:hover:bg-gray-600 flex items-center gap-2" role="menuitem" title="Mark all tasks as complete">
                     <i class="fas fa-check-double w-4"></i> Mark All Complete
                  </button>
                </template>
                <!-- Toggle View -->
                <button @click="toggleView(); showSettingsMenu = false" class="dropdown-menu-item w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 !dark:hover:bg-gray-600 flex items-center gap-2" role="menuitem" :title="isListView ? 'Switch to Card View' : 'Switch to List View'">
                   <i :class="isListView ? 'fas fa-th-large w-4' : 'fas fa-list w-4'"></i> {{ isListView ? 'Card View' : 'List View' }}
                </button>
                <!-- Toggle Dark Mode -->
                <button @click="toggleDarkMode(); showSettingsMenu = false" class="dropdown-menu-item w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 !dark:hover:bg-gray-600 flex items-center gap-2" role="menuitem" :title="darkMode ? 'Switch to Light Mode' : 'Switch to Dark Mode'">
                   <i :class="darkMode ? 'fas fa-sun w-4' : 'fas fa-moon w-4'"></i> {{ darkMode ? 'Light Mode' : 'Dark Mode' }}
                 </button>
                <!-- Replay Tutorial -->
                <button @click="startTodoTutorial(); showSettingsMenu = false" class="dropdown-menu-item w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 !dark:hover:bg-gray-600 flex items-center gap-2" role="menuitem" title="Replay the application tutorial">
                   <i class="fas fa-question-circle w-4"></i> Replay Tutorial
                </button>
              </div>
            </div>
          </div>

          <!-- Auth Buttons -->
          <button v-if="!isAuthenticated"
            @click="showAuthModal = true"
            class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded-xl focus:outline-none focus:shadow-outline flex items-center justify-center gap-2"
            title="Login or Register">
            <i class="fas fa-user"></i>
            <span>Login</span>
          </button>
          <div v-else class="relative">
            <button @click="userMenuOpen = !userMenuOpen" class="bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-100 font-bold py-2 px-4 rounded-xl focus:outline-none focus:shadow-outline flex items-center gap-2" title="Open User Menu">
              <i class="fas fa-user-circle"></i>
              <span>{{ userName }}</span>
              <i class="fas fa-caret-down ml-1"></i>
            </button>
            <div v-if="userMenuOpen" class="absolute right-0 mt-2 w-40 rounded-md shadow-lg bg-white dark:bg-gray-700 ring-1 ring-black ring-opacity-5 z-30">
              <button @click="logout(); userMenuOpen = false" class="dropdown-menu-item rounded-md w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 flex items-center gap-2" title="Logout from your account">
                <i class="fas fa-sign-out-alt"></i> Logout
              </button>
            </div>
          </div>
        </div>
      </nav>

      <!-- Todo List/Cards -->
      <div v-if="isListView">
        <transition-group name="fade" tag="ul" class="list-none p-0 relative">
          <li v-for="todo in filteredTodos" :key="todo.id"
            class="bg-white shadow rounded-xl p-4 mb-2 flex items-center justify-between transition-colors duration-300 todo-card relative transform-gpu"
            draggable="true"
            @dragstart="(e) => { startDrag(todo); e.target.classList.add('dragging'); }"
            @dragend="(e) => e.target.classList.remove('dragging')"
            @dragover.prevent 
            @drop="onDrop(todo)">
            <div class="flex items-center">
              <label class="toggle-switch mr-2">
                <input type="checkbox" @change="handleCheckboxClick(todo)" :checked="todo.completed" title="Completion Status">
                <span class="slider"></span>
              </label>
              <span :class="{ 'line-through text-gray-500': todo.completed }">{{ todo.text }} ({{ todo.category }})</span>
            </div>
            <div>
              <button @click="editTodo(todo)" class="text-blue-500 hover:text-blue-700 mr-2" title="Edit Task"><i
                  class="fas fa-edit"></i></button>
              <button @click="deleteTodo(todo.id)" class="text-red-500 hover:text-red-700" title="Delete Task"><i
                  class="fas fa-trash"></i></button>
            </div>
          </li>
        </transition-group>
      </div>
      <div v-else class="flex flex-row flex-wrap gap-4 w-full">
        <transition-group name="slide" tag="div" class="flex flex-row flex-wrap gap-4 w-full relative">
          <div v-for="todo in filteredTodos" :key="todo.id"
            :class="['bg-white shadow rounded-xl p-4 flex flex-col justify-between todo-card transition-colors duration-300 relative transform-gpu', `priority-${todo.priority}`, 'min-w-[250px]', 'max-w-xs', 'flex-1']"
            draggable="true"
            @dragstart="(e) => { startDrag(todo); e.target.classList.add('dragging'); }"
            @dragend="(e) => e.target.classList.remove('dragging')"
            @dragover.prevent 
            @drop="onDrop(todo)">
            <div>
              <div class="flex justify-between items-start mb-2">
                <h3 :class="{ 'line-through text-gray-500': todo.completed }" class="font-bold">{{ todo.text }}</h3>
                <span v-if="todo.dueDate" class="text-xs text-gray-500">{{ formatDate(todo.dueDate) }}</span>
              </div>
              <p class="text-white-600 text-sm mb-2">Category: {{ todo.category }}</p>
              <div class="flex items-center">
                <label class="toggle-switch mr-2">
                  <input type="checkbox" @change="handleCheckboxClick(todo)" :checked="todo.completed">
                  <span class="slider"></span>
                </label>
                <span class="text-sm">Completed</span>
              </div>
              <div class="progress-bar" v-if="todo.subtasks && todo.subtasks.length > 0">
                <div class="progress-fill" :style="{ width: `${getProgress(todo)}%` }"></div>
              </div>
            </div>
            <div class="mt-4 flex justify-between items-center">
              <span class="text-xs font-semibold px-2 py-1 rounded-xl"
                :class="{
                  'bg-red-100 text-red-800': todo.priority === 'high',
                  'bg-yellow-100 text-yellow-800': todo.priority === 'medium',
                  'bg-green-100 text-green-800': todo.priority === 'low'
                }">
                {{ todo.priority.toUpperCase() }} PRIORITY
              </span>
              <div>
                <button @click="editTodo(todo)" class="text-blue-500 hover:text-blue-700 mr-2" title="Edit Task"><i
                    class="fas fa-edit"></i></button>
                <button @click="deleteTodo(todo.id)" class="text-red-500 hover:text-red-700" title="Delete Task"><i
                    class="fas fa-trash"></i></button>
              </div>
            </div>
          </div>
        </transition-group>
      </div>

      <!-- Add Todo Modal -->
      <transition name="modal-pop">
        <div v-if="showAddModal" class="fixed top-0 left-0 w-full h-full bg-gray-900 bg-opacity-50 dark:bg-opacity-70 backdrop-blur-sm z-50 transition-all duration-300 flex items-center justify-center z-50">
          <div class="bg-white rounded-xl shadow-lg p-8 w-full max-w-md relative">
            <button @click="showAddModal = false" class="absolute top-2 right-2 text-gray-400 hover:text-gray-700 text-xl" title="Close Add Task Modal">
              <i class="fas fa-times"></i>
            </button>
            <h2 class="text-2xl font-bold mb-4">Add New Todo</h2>
          <input type="text" v-model="newTodo" @keyup.enter="addTodo"
            class="shadow appearance-none border rounded-xl w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline mb-2"
            placeholder="Add a new task...">
          <div class="flex space-x-2 mb-2">
            <select v-model="newTodoCategory"
              class="shadow appearance-none border rounded-xl w-1/2 py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
              <option v-for="category in categories" :key="category.id" :value="category">{{ category.name }}</option>
            </select>
            <select v-model="newTodoPriority"
              class="shadow appearance-none border rounded-xl w-1/2 py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
              <option v-for="priority in priorities" :key="priority.name" :value="priority">{{ priority.name.toUpperCase() }} PRIORITY</option>
            </select>
          </div>
          <input type="date" v-model="newTodoDueDate"
            class="shadow appearance-none border rounded-xl w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline mb-4">
          <div class="flex justify-end">
            <button @click="addTodo" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-xl focus:outline-none focus:shadow-outline mr-2" title="Add New Task">Add</button>
            <button @click="showAddModal = false" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-xl focus:outline-none focus:shadow-outline" title="Cancel Adding Task">Cancel</button>
          </div>
        </div>
      </div>
      </transition>

      <!-- Add Category Modal -->
      <transition name="modal-pop">
        <div v-if="showAddCategoryModal" class="fixed top-0 left-0 w-full h-full bg-gray-900 bg-opacity-50 dark:bg-opacity-70 backdrop-blur-sm flex items-center justify-center z-50">
          <div class="bg-white rounded-xl shadow-lg p-8 w-full max-w-md relative">
            <button @click="showAddCategoryModal = false" class="absolute top-2 right-2 text-gray-400 hover:text-gray-700 text-xl" title="Close Add Category Modal">
              <i class="fas fa-times"></i>
            </button>
            <h2 class="text-2xl font-bold mb-4">Add Category</h2>
          <input type="text" v-model="newCategoryName" @keyup.enter="addCategory" class="shadow appearance-none border rounded-xl w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline mb-4" placeholder="Category name...">
          <div class="flex justify-end">
            <button @click="addCategory" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-xl focus:outline-none focus:shadow-outline mr-2" title="Add New Category">Add</button>
            <button @click="showAddCategoryModal = false" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-xl focus:outline-none focus:shadow-outline" title="Cancel Adding Category">Cancel</button>
          </div>
        </div>
      </div>
      </transition>

      <!-- Edit Modal -->
      <transition name="modal-pop">
        <div v-if="editingTodo"
          class="fixed top-0 left-0 w-full h-full bg-gray-900 bg-opacity-50 dark:bg-opacity-70 backdrop-blur-sm flex items-center justify-center z-50">
          <div class="bg-white rounded-xl shadow-lg p-8 w-full max-w-md">
            <h2 class="text-2xl font-bold mb-4">Edit Todo</h2>
            <input type="text" v-model="editingTodo.text"
            class="shadow appearance-none border rounded-xl w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline mb-2">
          <div class="grid grid-cols-2 gap-2 mb-2">
            <select v-model="editingTodo.category"
              class="shadow appearance-none border rounded-xl w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
              <option v-for="category in categories" :key="category.id" :value="category.name">{{ category.name }}</option>
            </select>
            <select v-model="editingTodo.priority"
              class="shadow appearance-none border rounded-xl w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
              <option v-for="priority in priorities" :key="priority.name" :value="priority.name">{{ priority.name.toUpperCase() }} PRIORITY</option>
            </select>
          </div>
          <input type="date" v-model="editingTodo.dueDate"
            class="shadow appearance-none border rounded-xl w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline mb-4">
          <div class="mb-4" v-if="editingTodo.subtasks && editingTodo.subtasks.length > 0">
            <h3 class="font-semibold mb-2">Subtasks</h3>
            <div v-for="(subtask, index) in editingTodo.subtasks" :key="index" class="flex items-center mb-2">
              <label class="toggle-switch mr-2">
                <input type="checkbox" v-model="subtask.completed" @change="handleSubtaskToggle(subtask)" title="Toggle subtask completion">
                <span class="slider"></span>
              </label>
              <input type="text" v-model="subtask.text" @change="handleSubtaskTextChange(subtask)"
                class="shadow appearance-none border rounded-xl w-full py-1 px-2 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
              <button @click="removeSubtask(index)" class="ml-2 text-red-500 hover:text-red-700 transition-colors duration-200" title="Remove Subtask">
                <i class="fas fa-times"></i>
              </button>
            </div>
          </div>
          <button @click="addSubtask"
            class="bg-gray-200 hover:bg-gray-300 text-gray-800 py-1 px-3 rounded-xl text-sm mb-4" title="Add Subtask">
            + Add Subtask
          </button>
          <div class="flex justify-end">
            <button @click="updateTodo"
              class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-xl focus:outline-none focus:shadow-outline mr-2" title="Save Task Changes">Update</button>
            <button @click="cancelEdit"
              class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-xl focus:outline-none focus:shadow-outline" title="Cancel Editing Task">Cancel</button>
          </div>
        </div>
      </div>
      </transition>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <script src="https://unpkg.com/vue@3/dist/vue.global.prod.js"></script>
  <script>
    // Initialize loader
    (function() {
      const loader = document.getElementById('app-loader');
      const app = document.getElementById('app');
      
      // Hide app content initially
      app.style.visibility = 'hidden';
      
      // Function to check if all resources are loaded
      function checkResources() {
        return window.Vue && window.Swal && window.Shepherd;
      }
      
      // Function to show app and hide loader
      function showApp() {
        loader.classList.add('hidden');
        app.style.visibility = 'visible';
      }
      
      // Check resources periodically
      let attempts = 0;
      const maxAttempts = 50; // 5 seconds total
      
      function check() {
        attempts++;
        if (checkResources()) {
          showApp();
        } else if (attempts >= maxAttempts) {
          // Show error in loader
          loader.innerHTML = `
            <div class="p-6 max-w-md mx-auto bg-red-100 dark:bg-red-900/50 text-red-700 dark:text-red-200 rounded-lg shadow-md">
              <h2 class="text-xl font-bold mb-3">Failed to Load Resources</h2>
              <p class="mb-4">The application could not load required libraries. This may be due to network issues.</p>
              <button onclick="window.location.reload()" class="w-full bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded" title="Try Again">
                Try Again
              </button>
            </div>
          `;
        } else {
          setTimeout(check, 100);
        }
      }
      
      // Start checking
      check();
    })();

    const { createApp, ref, computed, onMounted, onUnmounted, watch } = Vue;

    createApp({
      setup() {
        const defaultTodos = [
          { id: 1, text: 'Learn Vue.js', completed: false, category: 'Learning', priority: 'medium', dueDate: null },
          { id: 2, text: 'Build a Todo App', completed: true, category: 'Projects', priority: 'high', dueDate: null },
          { id: 3, text: 'Style with Tailwind CSS', completed: false, category: 'Design', priority: 'low', dueDate: null }
        ];
        const categories = ref([]); // Will be loaded from backend or localStorage
const startTodoTutorial = () => {
          if (typeof window.startTodoTutorialImpl === 'function') {
            window.startTodoTutorialImpl();
          } else {
            console.warn('startTodoTutorialImpl is not defined on window.');
            if (window.Swal) {
                 Swal.fire('Tutorial unavailable', 'The tutorial feature is currently not working.', 'info');
            }
          }
        };
        const priorities = ref([
          { name: 'low' },
          { name: 'medium' },
          { name: 'high' }
        ]);
        const todos = ref([]);
        const newTodo = ref('');
        const newTodoCategory = ref(null); // Will hold the selected category object
        const newTodoPriority = ref(priorities.value[1]); // default to medium
        const newTodoDueDate = ref(null);
        const editingTodo = ref(null);
        const isListView = ref(JSON.parse(localStorage.getItem('isListView')) ?? true);
        const selectedCategory = ref(null);
        const draggedTodo = ref(null);
        const searchQuery = ref('');
        const showAddModal = ref(false);
        const showAuthModal = ref(false);
        const showAddCategoryModal = ref(false);
        const newCategoryName = ref('');
        const darkMode = ref(JSON.parse(localStorage.getItem('darkMode')) ?? false);
        const isAuthenticated = ref(false);
        const authMode = ref('login');
        const loginEmail = ref('');
        const loginPassword = ref('');
        const registerName = ref('');
        const registerEmail = ref('');
        const registerPassword = ref('');
        const userName = ref('');
const settingsMenuRef = ref(null); // Template ref for the settings menu

        const handleClickOutside = (event) => {
          // Close menu if it's open and the click is outside the menu element
          if (showSettingsMenu.value && settingsMenuRef.value && !settingsMenuRef.value.contains(event.target)) {
            // Also check if the click was on the toggle button itself, otherwise it closes immediately
            const settingsButton = document.querySelector('#settings-button'); // Assuming we add an ID
             if (!settingsButton || !settingsButton.contains(event.target)) {
                showSettingsMenu.value = false;
            }
          }
        };

        onMounted(() => {
          document.addEventListener('mousedown', handleClickOutside);
          // The startTodoTutorial const will be defined below, outside onMounted, and returned from setup.
        });

        onUnmounted(() => {
          document.removeEventListener('mousedown', handleClickOutside);
        });
        const forgotEmail = ref('');
        const authError = ref('');
const showSettingsMenu = ref(false);
const userMenuOpen = ref(false);
        const canAddTodo = computed(() => isAuthenticated.value ? categories.value.length > 0 : true);

        // Function to handle API errors and show login modal when token is invalid/expired
        async function handleApiError(error) {
          console.error('API Error:', error);
          if (error.status === 401 || error.status === 403) {
            // Token expired or invalid
            isAuthenticated.value = false;
            localStorage.removeItem('todo_token');
            localStorage.removeItem('todo_user_name');
            showAuthModal.value = true;
            authMode.value = 'login';
            authError.value = 'Your session has expired. Please log in again.';
            return true; // Indicates that we've handled an auth error
          }
          return false; // Not an auth error
        }

        const filteredTodos = computed(() => {
          let filtered = todos.value;
          if (selectedCategory.value) filtered = filtered.filter(t => t.category === selectedCategory.value);
          if (searchQuery.value) {
            const q = searchQuery.value.toLowerCase();
            filtered = filtered.filter(t => t.text.toLowerCase().includes(q) || t.category.toLowerCase().includes(q));
          }
          // Always sort by display_order after filtering
          return filtered.sort((a, b) => (a.display_order ?? 0) - (b.display_order ?? 0));
        });
        const activeCount = computed(() => todos.value.filter(t => !t.completed).length);
        const completedCount = computed(() => todos.value.filter(t => t.completed).length);

        onMounted(async () => {
          applyDarkMode();
          setSwalTheme();
          const token = localStorage.getItem('todo_token');
          if (token) {
            isAuthenticated.value = true;
            await loadTodosFromApi();
            // Try to restore userName from localStorage if present
            const storedName = localStorage.getItem('todo_user_name');
            if (storedName) userName.value = storedName;
          } else {
            // Not logged in: use localStorage for todos
            todos.value = JSON.parse(localStorage.getItem('todos')) || defaultTodos;
            isAuthenticated.value = false;
            // Don't show modal by default
            showAuthModal.value = false;
          }
        });

        watch(todos, {
          handler: 'saveToLocalStorage',
          deep: true
        });

        async function addTodo() {
          if (isAuthenticated.value && categories.value.length === 0) {
            Swal.fire('No categories', 'Please create a category before adding tasks.', 'warning');
            return;
          }
          if (!newTodo.value.trim() || !newTodoCategory.value || !newTodoPriority.value) {
            Swal.fire('Missing data', 'Please enter a task and select a category and priority.', 'warning');
            return;
          }
          const token = localStorage.getItem('todo_token');
          if (!isAuthenticated.value) {
            // Local mode
            todos.value.push({
              id: Date.now(),
              text: newTodo.value,
              completed: false,
              category: newTodoCategory.value ? newTodoCategory.value.name : '',
              priority: newTodoPriority.value ? newTodoPriority.value.name : '',
              dueDate: newTodoDueDate.value,
              subtasks: []
            });
            resetAddForm();
            saveToLocalStorage();
            return;
          }
          try {
            const res = await fetch('/api/todo/tasks', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + token
              },
              body: JSON.stringify({
                text: newTodo.value,
                categoryId: newTodoCategory.value.id,
                priority: newTodoPriority.value.name,
                dueDate: newTodoDueDate.value
              })
            });
            const data = await res.json();
            if (!res.ok) {
              const error = new Error(data.error || 'Failed to add task');
              error.status = res.status;
              throw error;
            }
            // Add to local todos for display
            todos.value.push({
              ...data,
              category: newTodoCategory.value.name,
              priority: newTodoPriority.value.name,
              dueDate: data.due_date ? data.due_date.split('T')[0] : newTodoDueDate.value
            });
            resetAddForm();
          } catch (e) {
            if (!(await handleApiError(e))) {
              authError.value = e.message;
              Swal.fire('Error', e.message, 'error');
            }
          }
        }

        async function addCategory() {
          if (!newCategoryName.value.trim()) return;
          if (!isAuthenticated.value) {
            // Local mode
            categories.value.push({ id: Date.now(), name: newCategoryName.value });
            newCategoryName.value = '';
            showAddCategoryModal.value = false;
            return;
          }
          try {
            const token = localStorage.getItem('todo_token');
            const res = await fetch('/api/todo/categories', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + token
              },
              body: JSON.stringify({ name: newCategoryName.value })
            });
            const data = await res.json();
            if (!res.ok) {
              const error = new Error(data.error || 'Failed to add category');
              error.status = res.status;
              throw error;
            }
            categories.value.push({ id: data.id, name: data.name });
            newCategoryName.value = '';
            showAddCategoryModal.value = false;
          } catch (e) {
            if (!(await handleApiError(e))) {
              authError.value = e.message;
              Swal.fire('Error', e.message, 'error');
            }
          }
        }

        function resetAddForm() {
          newTodo.value = '';
          newTodoCategory.value = categories.value[0] || null;
          newTodoPriority.value = priorities.value[1];
          newTodoDueDate.value = null;
          showAddModal.value = false;
        }

        async function deleteTodo(id) {
          const result = await Swal.fire({
            title: 'Are you sure?',
            text: 'Do you want to delete this task?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Yes, delete it!'
          });

          if (result.isConfirmed) {
            if (!isAuthenticated.value) {
              todos.value = todos.value.filter(t => t.id !== id);
              saveToLocalStorage();
              Swal.fire('Deleted!', 'Your task has been deleted.', 'success');
            } else {
              const token = localStorage.getItem('todo_token');
              try {
                const res = await fetch(`/api/todo/tasks/${id}`, {
                  method: 'DELETE',
                  headers: { 'Authorization': 'Bearer ' + token }
                });
                if (!res.ok) {
                  const error = new Error('Failed to delete task');
                  error.status = res.status;
                  throw error;
                }
                todos.value = todos.value.filter(t => t.id !== id);
                Swal.fire('Deleted!', 'Your task has been deleted.', 'success');
              } catch (e) {
                if (!(await handleApiError(e))) {
                  authError.value = e.message;
                  Swal.fire('Error!', 'Failed to delete task: ' + e.message, 'error');
                }
              }
            }
          }
        }

        async function markAllComplete() {
          const result = await Swal.fire({
            title: 'Mark all as complete?',
            text: 'This will mark all tasks as completed.',
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#10b981',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Yes, mark all!'
          });
          if (result.isConfirmed) {
            // For demo: just update local, for real: implement batch API
            todos.value.forEach(t => t.completed = true);
            Swal.fire('All done!', 'All tasks marked as complete.', 'success');
          }
        }

        function editTodo(todo) {
          editingTodo.value = {
            ...todo,
            category: categories.value.find(c => c.name === todo.category)?.name || '',
            priority: priorities.value.find(p => p.name === todo.priority)?.name || 'medium',
            dueDate: todo.dueDate,
            subtasks: todo.subtasks || []
          };
        }

        async function updateTodo() {
          if (!editingTodo.value) return;
          const idx = todos.value.findIndex(t => t.id === editingTodo.value.id);
          if (idx === -1) return;
          
          if (!isAuthenticated.value) {
            todos.value.splice(idx, 1, { ...editingTodo.value });
            cancelEdit();
            saveToLocalStorage();
            return;
          }
          
          try {
            const token = localStorage.getItem('todo_token');
            const categoryObj = categories.value.find(c => c.name === editingTodo.value.category);
            
            // Update the main task
            const res = await fetch(`/api/todo/tasks/${editingTodo.value.id}`, {
              method: 'PUT',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + token
              },
              body: JSON.stringify({
                text: editingTodo.value.text,
                categoryId: categoryObj ? categoryObj.id : null,
                priority: editingTodo.value.priority,
                dueDate: editingTodo.value.dueDate
              })
            });
            
            if (!res.ok) {
              const error = new Error('Failed to update task');
              error.status = res.status;
              throw error;
            }
            
            const data = await res.json();
            todos.value.splice(idx, 1, {
              ...data,
              category: categoryObj ? categoryObj.name : '',
              priority: data.priority,
              dueDate: data.due_date ? data.due_date.split('T')[0] : editingTodo.value.dueDate,
              subtasks: editingTodo.value.subtasks
            });
            
            // Update all subtasks
            if (editingTodo.value.subtasks) {
              for (const subtask of editingTodo.value.subtasks) {
                await updateSubtask(subtask);
              }
            }
            
            cancelEdit();
          } catch (e) {
            if (!(await handleApiError(e))) {
              authError.value = e.message;
              Swal.fire('Error', e.message, 'error');
            }
          }
        }

        function cancelEdit() { editingTodo.value = null; }

        function toggleView() {
          isListView.value = !isListView.value;
          localStorage.setItem('isListView', JSON.stringify(isListView.value));
        }

        function toggleDarkMode() {
          darkMode.value = !darkMode.value;
          localStorage.setItem('darkMode', JSON.stringify(darkMode.value));
          applyDarkMode();
          setSwalTheme();
        }

        function applyDarkMode() {
          const body = document.body;
          if (darkMode.value) {
            document.documentElement.classList.add('dark');
            body.classList.add('dark', 'transition-bg');
            body.classList.remove('bg-gray-100', 'text-gray-800');
          } else {
            document.documentElement.classList.remove('dark');
            body.classList.remove('dark');
            body.classList.add('bg-gray-100', 'text-gray-800', 'transition-bg');
          }
          setSwalTheme();
        }

        function setSwalTheme() {
          if (window.Swal) {
            // Only patch Swal.fire once
            if (!Swal.__fireOrig) {
              Swal.__fireOrig = Swal.fire;
              Swal.fire = function (optionsOrTitle, html, icon) {
                let configArg;
                // Check if the first argument is a configuration object
                if (typeof optionsOrTitle === 'object' && optionsOrTitle !== null && !optionsOrTitle.then) {
                  configArg = { ...optionsOrTitle }; // Clone to prevent modifying the original
                } else {
                  // First argument is title (string) or a Promise from a previous Swal call.
                  // Construct a new config object for string arguments (title, html, icon).
                  configArg = { title: optionsOrTitle };
                  if (html !== undefined) configArg.html = html;
                  if (icon !== undefined) configArg.icon = icon;
                  // If optionsOrTitle is a Promise, this structure might need more advanced handling
                  // for theming, but for direct calls like Swal.fire('title','text','icon'), this works.
                }

                // Ensure customClass exists on our working configArg
                if (typeof configArg === 'object' && configArg !== null) { // Ensure configArg is an object before proceeding
                    if (!configArg.customClass) {
                        configArg.customClass = {};
                    }

                    // Apply dark mode class to popup
                    let existingPopupClass = configArg.customClass.popup || '';
                    configArg.customClass.popup = (darkMode.value ? 'swal2-dark' : '') + (existingPopupClass ? ' ' + existingPopupClass : '');
                    
                    // Apply dark mode background and color, allowing overrides by user if they set them explicitly
                    if (configArg.background === undefined) {
                        configArg.background = darkMode.value ? '#23272f' : '#fff';
                    }
                    if (configArg.color === undefined) {
                        configArg.color = darkMode.value ? '#f3f4f6' : '#23272f';
                    }
                }
                
                // Call the original Swal.fire.
                // If the original call was Swal.fire(optionsObject), configArg is the modified options.
                // If the original call was Swal.fire(title, html, icon), configArg is now an options object.
                // SweetAlert2's original fire method can handle an options object.
                // If optionsOrTitle was a Promise, we pass it and subsequent args directly.
                if (typeof optionsOrTitle === 'object' && optionsOrTitle !== null && !optionsOrTitle.then) {
                    return Swal.__fireOrig(configArg);
                } else if (typeof optionsOrTitle !== 'object' && html === undefined && icon === undefined && optionsOrTitle && optionsOrTitle.then) {
                    // This case handles Swal.fire(Promise) - pass through without our configArg
                    return Swal.__fireOrig(optionsOrTitle);
                } else {
                    // This handles Swal.fire(title, html, icon) and similar, using our constructed configArg
                    return Swal.__fireOrig(configArg);
                }
              };
            }
            // Update open dialog if present
            const swalPopup = document.querySelector('.swal2-popup');
            if (swalPopup) {
              if (darkMode.value) {
                swalPopup.classList.add('swal2-dark');
                swalPopup.style.background = '#23272f';
                swalPopup.style.color = '#f3f4f6';
              } else {
                swalPopup.classList.remove('swal2-dark');
                swalPopup.style.background = '#fff';
                swalPopup.style.color = '#23272f';
              }
            }
          }
        }

        function startDrag(todo) { 
          draggedTodo.value = todo; 
        }

        function onDrop(targetTodo) {
          if (draggedTodo.value && draggedTodo.value !== targetTodo) {
            const draggedIdx = todos.value.indexOf(draggedTodo.value);
            const targetIdx = todos.value.indexOf(targetTodo);
            todos.value.splice(draggedIdx, 1);
            todos.value.splice(targetIdx, 0, draggedTodo.value);
            // Update display_order locally
            todos.value.forEach((t, i) => t.display_order = i);
            if (isAuthenticated.value) {
              // Send new order to backend
              updateTaskOrderOnServer();
            } else {
              saveToLocalStorage();
            }
            draggedTodo.value = null;
          }
        }

        async function updateTaskOrderOnServer() {
          const token = localStorage.getItem('todo_token');
          try {
            await fetch('/api/todo/tasks/reorder', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + token
              },
              body: JSON.stringify({
                order: todos.value.map(t => ({ id: t.id, display_order: t.display_order }))
              })
            });
          } catch (e) {
            // Optionally show error
          }
        }

        async function addSubtask() {
          if (!editingTodo.value.subtasks) editingTodo.value.subtasks = [];
          if (!isAuthenticated.value) {
            editingTodo.value.subtasks.push({ text: '', completed: false });
            return;
          }
          
          const { value: text } = await Swal.fire({
            title: 'Add Subtask',
            input: 'text',
            inputPlaceholder: 'Enter subtask text',
            showCancelButton: true,
            inputValidator: (value) => {
              if (!value) {
                return 'Subtask text cannot be empty!';
              }
            }
          });

          if (text) {
            try {
              const token = localStorage.getItem('todo_token');
              const res = await fetch(`/api/todo/tasks/${editingTodo.value.id}/subtasks`, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  'Authorization': 'Bearer ' + token
                },
                body: JSON.stringify({ text })
              });
              
              if (!res.ok) {
                const error = new Error('Failed to add subtask');
                error.status = res.status;
                throw error;
              }
              
              const subtask = await res.json();
              editingTodo.value.subtasks.push(subtask);
            } catch (e) {
              if (!(await handleApiError(e))) {
                authError.value = e.message;
                Swal.fire('Error', e.message, 'error');
              }
            }
          }
        }

        async function removeSubtask(idx) {
          const subtask = editingTodo.value.subtasks[idx];
          if (!isAuthenticated.value) {
            editingTodo.value.subtasks.splice(idx, 1);
            return;
          }

          try {
            const token = localStorage.getItem('todo_token');
            const res = await fetch(`/api/todo/subtasks/${subtask.id}`, {
              method: 'DELETE',
              headers: {
                'Authorization': 'Bearer ' + token
              }
            });
            
            if (!res.ok) {
              const error = new Error('Failed to delete subtask');
              error.status = res.status;
              throw error;
            }
            
            editingTodo.value.subtasks.splice(idx, 1);
          } catch (e) {
            if (!(await handleApiError(e))) {
              authError.value = e.message;
              Swal.fire('Error', e.message, 'error');
            }
          }
        }

        async function updateSubtask(subtask) {
          if (!isAuthenticated.value) return;
          
          try {
            const token = localStorage.getItem('todo_token');
            const res = await fetch(`/api/todo/subtasks/${subtask.id}`, {
              method: 'PUT',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + token
              },
              body: JSON.stringify({
                text: subtask.text,
                completed: subtask.completed
              })
            });
            
            if (!res.ok) {
              const error = new Error('Failed to update subtask');
              error.status = res.status;
              throw error;
            }
          } catch (e) {
            if (!(await handleApiError(e))) {
              authError.value = e.message;
              Swal.fire('Error', e.message, 'error');
            }
          }
        }

        function formatDate(dateString) {
          if (!dateString) return '';
          return new Date(dateString).toLocaleDateString(undefined, { year: 'numeric', month: 'short', day: 'numeric' });
        }

        function getProgress(todo) {
          if (!todo.subtasks?.length) return 0;
          const done = todo.subtasks.filter(st => st.completed).length;
          return Math.round((done / todo.subtasks.length) * 100);
        }

        function saveToLocalStorage() { 
          localStorage.setItem('todos', JSON.stringify(todos.value)); 
        }

        async function login() {
          if (!loginEmail.value || !loginPassword.value) {
            authError.value = 'Please fill in all fields.';
            return;
          }
          try {
            const res = await fetch('/api/todo/login', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ email: loginEmail.value, password: loginPassword.value })
            });
            const data = await res.json();
            if (!res.ok) {
              const error = new Error(data.error || 'Login failed');
              error.status = res.status;
              throw error;
            }
            localStorage.setItem('todo_token', data.token);
            isAuthenticated.value = true;
            authError.value = '';
            showAuthModal.value = false;
            userName.value = data.name || loginEmail.value.split('@')[0];
            localStorage.setItem('todo_user_name', userName.value);
            const localTodos = JSON.parse(localStorage.getItem('todos')) || [];
            for (const t of localTodos) {
              await fetch('/api/todo/tasks', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  'Authorization': 'Bearer ' + data.token
                },
                body: JSON.stringify({
                  text: t.text,
                  categoryId: categories.value.find(c => c.name === t.category)?.id || null,
                  priority: priorities.value.find(p => p.name === t.priority)?.name || null,
                  dueDate: t.dueDate
                })
              });
            }
            localStorage.removeItem('todos');
            await loadTodosFromApi();
          } catch (e) {
            if (!(await handleApiError(e))) {
              authError.value = e.message;
              Swal.fire('Error', e.message, 'error');
            }
          }
        }

        async function register() {
          if (!registerEmail.value || !registerPassword.value) {
            authError.value = 'Please fill in all fields.';
            return;
          }
          try {
            const localTodos = JSON.parse(localStorage.getItem('todos')) || [];
            const res = await fetch('/api/todo/register', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                name: registerName.value,
                email: registerEmail.value,
                password: registerPassword.value
              })
            });
            const data = await res.json();
            if (!res.ok) {
              const error = new Error(data.error || 'Registration failed');
              error.status = res.status;
              throw error;
            }
            localStorage.setItem('todo_token', data.token);
            isAuthenticated.value = true;
            authError.value = '';
            showAuthModal.value = false;
            userName.value = registerName.value || registerEmail.value.split('@')[0];
            localStorage.setItem('todo_user_name', userName.value);
            for (const t of localTodos) {
              await fetch('/api/todo/tasks', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  'Authorization': 'Bearer ' + data.token
                },
                body: JSON.stringify({
                  text: t.text,
                  categoryId: categories.value.find(c => c.name === t.category)?.id || null,
                  priority: priorities.value.find(p => p.name === t.priority)?.name || null,
                  dueDate: t.dueDate
                })
              });
            }
            localStorage.removeItem('todos');
            await loadTodosFromApi();
          } catch (e) {
            if (!(await handleApiError(e))) {
              authError.value = e.message;
              Swal.fire('Error', e.message, 'error');
            }
          }
        }

        async function forgotPassword() {
          if (!forgotEmail.value) {
            authError.value = 'Please provide your email.';
            return;
          }
          try {
            const res = await fetch('/api/todo/forgot-password', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ email: forgotEmail.value })
            });
            const data = await res.json();
            if (!res.ok) {
              const error = new Error(data.error || 'Request failed');
              error.status = res.status;
              throw error;
            }
            authError.value = data.message || 'If this email exists, a reset link will be sent.';
          } catch (e) {
            if (!(await handleApiError(e))) {
              authError.value = e.message;
              Swal.fire('Error', e.message, 'error');
            }
          }
        }

        async function loadTodosFromApi() {
          const token = localStorage.getItem('todo_token');
          if (!token) return;
          try {
            const res = await fetch('/api/todo/load', {
              headers: { 'Authorization': 'Bearer ' + token }
            });
            if (!res.ok) {
              const error = new Error('Failed to load todos');
              error.status = res.status;
              throw error;
            }
            const data = await res.json();
            if (res.ok) {
              if (data.categories && data.categories.length) {
                categories.value = data.categories.map(c => ({ id: c.id, name: c.name }));
                newTodoCategory.value = categories.value[0] || null;
              } else {
                categories.value = [];
                newTodoCategory.value = null;
              }
              todos.value = (data.tasks || []).map(t => ({
                ...t,
                category: categories.value.find(c => c.id === t.category_id)?.name || '',
                priority: t.priority || 'medium',
                dueDate: t.due_date ? t.due_date.split('T')[0] : null
              }));
              newTodoPriority.value = priorities.value[1];
              if (data.user && data.user.name) {
                userName.value = data.user.name;
                localStorage.setItem('todo_user_name', userName.value);
              }
            } else {
              throw new Error(data.error || 'Failed to load todos');
            }
          } catch (e) {
            if (!(await handleApiError(e))) {
              authError.value = e.message;
              Swal.fire('Error', e.message, 'error');
            }
          }
        }

        function logout() {
          localStorage.removeItem('todo_token');
          localStorage.removeItem('todo_user_name');
          isAuthenticated.value = false;
          userName.value = '';
          todos.value = JSON.parse(localStorage.getItem('todos')) || defaultTodos;
          categories.value = [];
          selectedCategory.value = null;
          newTodoCategory.value = null;
          loginEmail.value = '';
          loginPassword.value = '';
          registerName.value = '';
          registerEmail.value = '';
          registerPassword.value = '';
          forgotEmail.value = '';
          authMode.value = 'login';
          showAuthModal.value = false;
          userMenuOpen.value = false;
        }

        async function editCategory(categoryId, newName) {
          if (!isAuthenticated.value) {
            const category = categories.value.find(c => c.id === categoryId);
            if (category) {
              category.name = newName;
              saveToLocalStorage();
            }
            return;
          }

          const token = localStorage.getItem('todo_token');
          try {
            const response = await fetch(`/api/todo/categories/${categoryId}`, {
              method: 'PUT',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + token
              },
              body: JSON.stringify({ name: newName })
            });

            if (!response.ok) {
              const data = await response.json();
              throw new Error(data.error || 'Failed to update category');
            }

            if (isAuthenticated.value) {
              await loadTodosFromApi();
            } else {
              saveToLocalStorage();
            }
          } catch (e) {
            Swal.fire('Error', e.message, 'error');
            if (isAuthenticated.value) {
               loadTodosFromApi();
            }
          }
        }

        async function deleteCategory(categoryId) {
          if (!isAuthenticated.value) {
            categories.value = categories.value.filter(c => c.id !== categoryId);
            todos.value = todos.value.filter(t => t.categoryId !== categoryId);
            saveToLocalStorage();
            return;
          }

          const token = localStorage.getItem('todo_token');
          try {
            const response = await fetch(`/api/todo/categories/${categoryId}`, {
              method: 'DELETE',
              headers: {
                'Authorization': 'Bearer ' + token
              }
            });

            if (!response.ok) {
              const data = await response.json();
              throw new Error(data.error || 'Failed to delete category');
            }

            todos.value = todos.value.filter(t => t.categoryId !== categoryId);
            categories.value = categories.value.filter(c => c.id !== categoryId);
          } catch (e) {
            Swal.fire('Error', e.message, 'error');
          }
        }

        async function handleEditCategory(category) {
          const { value: newName } = await Swal.fire({
            title: 'Edit Category',
            input: 'text',
            inputValue: category.name,
            showCancelButton: true,
            inputValidator: (value) => {
              if (!value) {
                return 'Category name cannot be empty!';
              }
            }
          });

          if (newName && newName !== category.name) {
            await editCategory(category.id, newName);
          }
        }

        async function handleDeleteCategory(category) {
          const result = await Swal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this! Do you want to delete this category?",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes, delete it!'
          });

          if (result.isConfirmed) {
            await deleteCategory(category.id);
          }
        }

        const toggleTaskComplete = async (todoId) => {
          const todo = todos.value.find(t => t.id === todoId);
          if (!todo) return;

          if (!isAuthenticated.value) {
            todo.completed = !todo.completed;
            saveToLocalStorage();
            return;
          }

          const token = localStorage.getItem('todo_token');
          try {
            const response = await fetch(`/api/todo/tasks/${todoId}/toggle`, {
              method: 'PATCH',
              headers: {
                'Authorization': 'Bearer ' + token
              }
            });

            if (!response.ok) {
              const data = await response.json();
              throw new Error(data.error || 'Failed to toggle task completion');
            }

            const data = await response.json();
            if (typeof data.completed !== 'undefined') {
              todo.completed = data.completed;
            } else {
              console.warn("Backend response for toggle task missing 'completed' field:", data);
            }
          } catch (e) {
            if (window.Swal) {
              Swal.fire('Error', e.message, 'error');
            } else {
              alert(e.message);
            }
            const taskToRevert = todos.value.find(t => t.id === todoId);
             if (taskToRevert) {
                  taskToRevert.completed = !taskToRevert.completed;
             }
          }
        };

        const handleCheckboxClick = async (todo) => {
          await toggleTaskComplete(todo.id);
        };

        onMounted(() => {
          document.addEventListener('mousedown', (event) => {
            if (userMenuOpen.value) {
              const userMenuBtn = document.querySelector('.fa-user-circle')?.parentElement;
              const userMenuDropdown = document.querySelector('.z-30');
              if (
                userMenuBtn && !userMenuBtn.contains(event.target) &&
                userMenuDropdown && !userMenuDropdown.contains(event.target)
              ) {
                userMenuOpen.value = false;
              }
            }
          });
        });

        async function handleSubtaskToggle(subtask) {
          if (!isAuthenticated.value) return;
          
          try {
            const token = localStorage.getItem('todo_token');
            const res = await fetch(`/api/todo/subtasks/${subtask.id}`, {
              method: 'PUT',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + token
              },
              body: JSON.stringify({
                text: subtask.text,
                completed: subtask.completed
              })
            });
            
            if (!res.ok) throw new Error('Failed to update subtask');
          } catch (e) {
            Swal.fire('Error', e.message, 'error');
            // Revert the toggle if the update failed
            subtask.completed = !subtask.completed;
          }
        }

        async function handleSubtaskTextChange(subtask) {
          if (!isAuthenticated.value) return;
          
          try {
            const token = localStorage.getItem('todo_token');
            const res = await fetch(`/api/todo/subtasks/${subtask.id}`, {
              method: 'PUT',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + token
              },
              body: JSON.stringify({
                text: subtask.text,
                completed: subtask.completed
              })
            });
            
            if (!res.ok) throw new Error('Failed to update subtask');
          } catch (e) {
            Swal.fire('Error', e.message, 'error');
          }
        }

        return {
          todos,
          newTodo,
          newTodoCategory,
          newTodoPriority,
          newTodoDueDate,
          editingTodo,
          isListView,
          categories,
          priorities,
          selectedCategory,
          draggedTodo,
          searchQuery,
          showAddModal,
          showAuthModal,
          showAddCategoryModal,
showSettingsMenu,
userMenuOpen,
          newCategoryName,
          darkMode,
          isAuthenticated,
          authMode,
          loginEmail,
          loginPassword,
          registerName,
          registerEmail,
settingsMenuRef,
          registerPassword,
          forgotEmail,
          authError,
          userName,
          canAddTodo,
          filteredTodos,
          activeCount,
          completedCount,
          addTodo,
          addCategory,
          resetAddForm,
          deleteTodo,
          markAllComplete,
          editTodo,
          updateTodo,
          cancelEdit,
          toggleView,
          toggleDarkMode,
          applyDarkMode,
          setSwalTheme,
          startDrag,
          onDrop,
          updateTaskOrderOnServer,
          addSubtask,
          removeSubtask,
          formatDate,
startTodoTutorial,
          getProgress,
          saveToLocalStorage,
          login,
          register,
          forgotPassword,
          loadTodosFromApi,
          logout,
          editCategory,
          deleteCategory,
          handleEditCategory,
          handleDeleteCategory,
          toggleTaskComplete,
          handleCheckboxClick,
          handleSubtaskToggle,
          handleSubtaskTextChange
        };
      }
    })
      .mount('#app');
  </script>
</body>
</html>