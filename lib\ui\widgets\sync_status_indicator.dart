import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/providers/todo_provider.dart';
import '../../core/services/sync_service.dart';
import '../../core/services/connectivity_service.dart';

/// A widget that displays the current sync status
class SyncStatusIndicator extends StatelessWidget {
  const SyncStatusIndicator({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final todoProvider = Provider.of<TodoProvider>(context);
    final syncService = Provider.of<SyncService>(context);
    final connectivityService = Provider.of<ConnectivityService>(context);

    // If not authenticated, don't show anything
    if (!todoProvider.isAuthenticated) {
      return const SizedBox.shrink();
    }

    // If online with no pending operations, show a checkmark
    if (connectivityService.isConnected &&
        syncService.pendingOperationsCount == 0) {
      return const Tooltip(
        message: 'All changes are synced',
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 8.0),
          child: Icon(
            Icons.cloud_done,
            color: Colors.green,
            size: 20,
          ),
        ),
      );
    }

    // If offline, show offline indicator
    if (!connectivityService.isConnected) {
      final pendingCount = syncService.pendingOperationsCount;
      final message = pendingCount > 0
          ? 'You are offline. $pendingCount changes will be synced when you reconnect.'
          : 'You are offline. Changes will be synced when you reconnect.';

      return Tooltip(
        message: message,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8.0),
          child: pendingCount > 0
              ? Badge(
                  label: Text(pendingCount.toString()),
                  child: const Icon(
                    Icons.cloud_off,
                    color: Colors.grey,
                    size: 20,
                  ),
                )
              : const Icon(
                  Icons.cloud_off,
                  color: Colors.grey,
                  size: 20,
                ),
        ),
      );
    }

    // If syncing, show a progress indicator
    if (syncService.isSyncing) {
      return Tooltip(
        message: 'Syncing ${syncService.pendingOperationsCount} changes...',
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8.0),
          child: SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                Theme.of(context).colorScheme.primary,
              ),
            ),
          ),
        ),
      );
    }

    // If online with pending operations, show a warning
    return Tooltip(
      message: '${syncService.pendingOperationsCount} changes waiting to sync',
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8.0),
        child: Badge(
          label: Text(syncService.pendingOperationsCount.toString()),
          child: const Icon(
            Icons.cloud_sync,
            color: Colors.orange,
            size: 20,
          ),
        ),
      ),
    );
  }
}
