import 'package:flutter/material.dart';
import '../../core/services/error_message_service.dart';

/// Shows a user-friendly error dialog
class ErrorDialog extends StatelessWidget {
  final dynamic error;
  final String? title;
  final VoidCallback? onRetry;
  
  const ErrorDialog({
    Key? key,
    required this.error,
    this.title,
    this.onRetry,
  }) : super(key: key);
  
  /// Show the error dialog
  static Future<void> show(
    BuildContext context, {
    required dynamic error,
    String? title,
    VoidCallback? onRetry,
  }) {
    return showDialog(
      context: context,
      builder: (context) => ErrorDialog(
        error: error,
        title: title,
        onRetry: onRetry,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final errorMessage = ErrorMessageService.getUserFriendlyMessage(error);
    
    return AlertDialog(
      title: Text(title ?? 'Error'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(errorMessage),
          if (onRetry != null) const SizedBox(height: 16),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Close'),
        ),
        if (onRetry != null)
          FilledButton(
            onPressed: () {
              Navigator.of(context).pop();
              onRetry!();
            },
            child: const Text('Retry'),
          ),
      ],
    );
  }
}

/// Shows a dialog for offline mode
class OfflineDialog extends StatelessWidget {
  final VoidCallback? onContinue;
  
  const OfflineDialog({
    Key? key,
    this.onContinue,
  }) : super(key: key);
  
  /// Show the offline dialog
  static Future<void> show(
    BuildContext context, {
    VoidCallback? onContinue,
  }) {
    return showDialog(
      context: context,
      builder: (context) => OfflineDialog(
        onContinue: onContinue,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('You are offline'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(ErrorMessageService.getOfflineErrorMessage()),
          const SizedBox(height: 16),
          const Text(
            'Your changes will be saved locally and synced when you reconnect.',
            style: TextStyle(fontStyle: FontStyle.italic),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        FilledButton(
          onPressed: () {
            Navigator.of(context).pop();
            if (onContinue != null) {
              onContinue!();
            }
          },
          child: const Text('Continue'),
        ),
      ],
    );
  }
}
