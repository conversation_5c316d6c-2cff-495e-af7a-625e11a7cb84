import 'package:flutter/foundation.dart';
import '../models/category.dart' as cat;

/// Service to handle category-related operations and standardize category ID resolution
class CategoryService {
  final List<cat.Category> _categories;

  CategoryService(this._categories);

  /// Update the categories list
  void updateCategories(List<cat.Category> categories) {
    _categories.clear();
    _categories.addAll(categories);
  }

  /// Get a category by name
  cat.Category? getCategoryByName(String name) {
    try {
      return _categories.firstWhere((c) => c.name == name);
    } catch (e) {
      debugPrint('Category not found: $name');
      return null;
    }
  }

  /// Get a category by ID
  cat.Category? getCategoryById(String id) {
    try {
      return _categories.firstWhere((c) => c.id == id);
    } catch (e) {
      debugPrint('Category not found with ID: $id');
      return null;
    }
  }

  /// Get category ID by name
  String? getCategoryIdByName(String name) {
    final category = getCategoryByName(name);
    return category?.id;
  }

  /// Get category index by name
  /// Returns a valid category ID (as an integer) or 0 if not found
  int getCategoryIndexByName(String name) {
    // First try to find the category by name
    final index = _categories.indexWhere((c) => c.name == name);

    // If found and the category has a valid ID, try to parse it
    if (index >= 0 && _categories[index].id.isNotEmpty) {
      try {
        return int.parse(_categories[index].id);
      } catch (e) {
        debugPrint('Could not parse category ID: ${_categories[index].id}');
      }
    }

    // If we couldn't find the category or parse its ID, return the index if it's valid
    if (index >= 0) {
      return index;
    }

    // If all else fails, return 0 (default category)
    debugPrint('Using default category ID (0) for: $name');
    return 0;
  }

  /// Get category name by ID
  String getCategoryNameById(String id) {
    final category = getCategoryById(id);
    return category?.name ?? 'Uncategorized';
  }

  /// Ensure a category exists, create it if it doesn't
  cat.Category ensureCategory(String name) {
    final existing = getCategoryByName(name);
    if (existing != null) {
      return existing;
    }

    // Create a new category with a temporary ID
    final newCategory = cat.Category(id: '', name: name, color: '#000000');
    _categories.add(newCategory);
    return newCategory;
  }

  /// Get all categories
  List<cat.Category> get categories => List.unmodifiable(_categories);
}
