import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../widgets/todo_list.dart';
import '../widgets/todo_grid.dart';
import '../widgets/category_filter.dart';
import '../widgets/search_bar.dart';
import '../widgets/add_todo_fab.dart';
import '../widgets/settings_menu.dart';
import '../widgets/auth_button.dart';
import '../widgets/sync_status_indicator.dart';
import '../widgets/sync_button.dart';
import '../../core/providers/todo_provider.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final todoProvider = Provider.of<TodoProvider>(context);

    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            Image.asset('assets/images/favicon.png', width: 28, height: 28),
            const SizedBox(width: 8),
            const Text('Todo App'),
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                'Beta',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
            ),
          ],
        ),
        actions: const [
          SyncStatusIndicator(),
          SyncButton(),
          AuthButton(),
          SettingsMenu(),
        ],
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                const Expanded(child: CustomSearchBar()),
                const SizedBox(width: 8),
                IconButton(
                  icon: Icon(
                    todoProvider.isListView ? Icons.grid_view : Icons.list,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  onPressed: todoProvider.toggleViewMode,
                  tooltip: todoProvider.isListView
                      ? 'Switch to Grid View'
                      : 'Switch to List View',
                ),
              ],
            ),
          ),
          const CategoryFilter(),
          Expanded(
            child:
                todoProvider.isListView ? const TodoList() : const TodoGrid(),
          ),
        ],
      ),
      floatingActionButton: const AddTodoFAB(),
    );
  }
}
