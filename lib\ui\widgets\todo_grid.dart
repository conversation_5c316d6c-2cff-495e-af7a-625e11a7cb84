import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:reorderables/reorderables.dart';
import '../../core/models/todo.dart';
import '../../core/providers/todo_provider.dart';
import '../../ui/theme/app_theme.dart';
import '../../ui/widgets/edit_todo_dialog.dart';

class TodoGrid extends StatefulWidget {
  const TodoGrid({super.key});

  @override
  State<TodoGrid> createState() => _TodoGridState();
}

class _TodoGridState extends State<TodoGrid> with TickerProviderStateMixin {
  late List<Todo> _previousTodos = [];

  @override
  Widget build(BuildContext context) {
    final todoProvider = Provider.of<TodoProvider>(context);
    final todos = todoProvider.filteredTodos;

    if (_previousTodos != todos) {
      _previousTodos = List.from(todos);
    }

    if (todos.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.task_alt,
              size: 64,
              color: Theme.of(context).colorScheme.primary.withOpacity(0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'No tasks yet',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: Theme.of(context)
                        .colorScheme
                        .onSurface
                        .withOpacity(0.5),
                  ),
            ),
          ],
        ),
      );
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          scrollDirection: Axis.vertical,
          child: ConstrainedBox(
            constraints: BoxConstraints(
                minWidth: constraints.maxWidth, maxWidth: constraints.maxWidth),
            child: ReorderableWrap(
              alignment: WrapAlignment.start,
              runAlignment: WrapAlignment.start,
              spacing: 16,
              runSpacing: 16,
              maxMainAxisCount:
                  (constraints.maxWidth / 320).floor().clamp(1, 6),
              needsLongPressDraggable: false,
              onReorder: todoProvider.reorderTodos,
              children: [
                for (final todo in todos)
                  TweenAnimationBuilder<double>(
                    key: ValueKey(todo.id),
                    tween: Tween(begin: 0.0, end: 1.0),
                    duration: const Duration(milliseconds: 400),
                    curve: Curves.easeOutCubic,
                    builder: (context, value, child) {
                      return Transform.scale(
                        scale: 0.95 + (value * 0.05),
                        child: Opacity(
                          opacity: value,
                          child: Transform.translate(
                            offset: Offset(0, 20 * (1 - value)),
                            child: SizedBox(
                              key: ValueKey('${todo.id}_item'),
                              width: 300,
                              height: 180,
                              child: TodoGridItem(todo: todo),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
              ],
            ),
          ),
        );
      },
    );
  }
}

class TodoGridItem extends StatelessWidget {
  final Todo todo;

  const TodoGridItem({
    super.key,
    required this.todo,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () {
          showDialog(
            context: context,
            builder: (context) => EditTodoDialog(todo: todo),
          );
        },
        borderRadius: BorderRadius.circular(12),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Colored border as a vertical bar inside the card, respects border radius
            Container(
              width: 6,
              decoration: BoxDecoration(
                color: AppTheme.priorityColors[todo.priority],
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  bottomLeft: Radius.circular(12),
                ),
              ),
            ),
            Expanded(
              child: Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 14, vertical: 14),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: Text(
                            todo.text,
                            style: TextStyle(
                              decoration: todo.completed
                                  ? TextDecoration.lineThrough
                                  : null,
                              color: todo.completed
                                  ? Theme.of(context)
                                      .colorScheme
                                      .onSurface
                                      .withOpacity(0.5)
                                  : Theme.of(context).colorScheme.onSurface,
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                    // Inline, smaller switch under todo text
                    Padding(
                      padding: const EdgeInsets.only(top: 4, bottom: 2),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Transform.translate(
                            offset: const Offset(-8, 0), // Shift switch left
                            child: Transform.scale(
                              scale: 0.7, // Make the switch smaller
                              child: Switch(
                                value: todo.completed,
                                onChanged: (value) {
                                  Provider.of<TodoProvider>(context,
                                          listen: false)
                                      .toggleTodoComplete(todo.id);
                                },
                                activeColor:
                                    Theme.of(context).colorScheme.primary,
                                inactiveThumbColor: Theme.of(context)
                                    .colorScheme
                                    .surfaceContainerHighest,
                                inactiveTrackColor: Theme.of(context)
                                    .colorScheme
                                    .outlineVariant
                                    .withOpacity(0.5),
                                thumbColor:
                                    WidgetStateProperty.resolveWith<Color>(
                                        (states) {
                                  if (states.contains(WidgetState.selected)) {
                                    return Theme.of(context)
                                        .colorScheme
                                        .primary;
                                  }
                                  return Theme.of(context)
                                      .colorScheme
                                      .onSurfaceVariant;
                                }),
                                trackColor:
                                    WidgetStateProperty.resolveWith<Color>(
                                        (states) {
                                  if (states.contains(WidgetState.selected)) {
                                    return Theme.of(context)
                                        .colorScheme
                                        .primary
                                        .withOpacity(0.5);
                                  }
                                  return Theme.of(context)
                                      .colorScheme
                                      .outlineVariant
                                      .withOpacity(0.2);
                                }),
                                materialTapTargetSize:
                                    MaterialTapTargetSize.shrinkWrap,
                              ),
                            ),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            todo.completed ? 'Completed' : 'Active',
                            style: TextStyle(
                              fontSize: 15,
                              color: Theme.of(context)
                                  .colorScheme
                                  .onSurfaceVariant,
                            ),
                          ),
                        ],
                      ),
                    ),
                    if (todo.subtasks.isNotEmpty) ...[
                      const SizedBox(height: 10),
                      ClipRRect(
                        borderRadius: BorderRadius.circular(4),
                        child: LinearProgressIndicator(
                          value: todo.progress,
                          backgroundColor: Theme.of(context)
                              .colorScheme
                              .surfaceContainerHighest
                              .withOpacity(0.5),
                          minHeight: 5,
                        ),
                      ),
                    ],
                    const Spacer(),
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 10,
                            vertical: 3,
                          ),
                          decoration: BoxDecoration(
                            color: Theme.of(context)
                                .colorScheme
                                .surfaceContainerHighest,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            todo.category,
                            style: TextStyle(
                              fontSize: 13,
                              color: Theme.of(context)
                                  .colorScheme
                                  .onSurfaceVariant,
                            ),
                          ),
                        ),
                        if (todo.dueDate != null) ...[
                          const SizedBox(width: 10),
                          Icon(
                            Icons.calendar_today,
                            size: 15,
                            color:
                                Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            DateFormat('MMM d, y').format(todo.dueDate!),
                            style: TextStyle(
                              fontSize: 13,
                              color: Theme.of(context)
                                  .colorScheme
                                  .onSurfaceVariant,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
