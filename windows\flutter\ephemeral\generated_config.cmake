# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\Users\\<USER>\\flutter\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "F:\\My Apps\\todoapp\\todoapp" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\Users\\<USER>\\flutter\\flutter"
  "PROJECT_DIR=F:\\My Apps\\todoapp\\todoapp"
  "FLUTTER_ROOT=C:\\Users\\<USER>\\flutter\\flutter"
  "FLUTTER_EPHEMERAL_DIR=F:\\My Apps\\todoapp\\todoapp\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=F:\\My Apps\\todoapp\\todoapp"
  "FLUTTER_TARGET=F:\\My Apps\\todoapp\\todoapp\\lib\\main.dart"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=F:\\My Apps\\todoapp\\todoapp\\.dart_tool\\package_config.json"
)
