import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'api_service.dart';
import 'connectivity_service.dart';

/// Enum representing the type of pending operation
enum PendingOperationType {
  addTodo,
  updateTodo,
  deleteTodo,
  toggleTodoComplete,
  addCategory,
  updateCategory,
  deleteCategory,
  addSubtask,
  updateSubtask,
  deleteSubtask,
  reorderTodos,
}

/// Class representing a pending operation to be synced
class PendingOperation {
  final PendingOperationType type;
  final Map<String, dynamic> data;
  final DateTime timestamp;

  PendingOperation({
    required this.type,
    required this.data,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  Map<String, dynamic> toJson() {
    return {
      'type': type.toString(),
      'data': data,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  factory PendingOperation.fromJson(Map<String, dynamic> json) {
    return PendingOperation(
      type: PendingOperationType.values.firstWhere(
        (e) => e.toString() == json['type'],
        orElse: () => PendingOperationType.addTodo,
      ),
      data: json['data'] as Map<String, dynamic>,
      timestamp: DateTime.parse(json['timestamp'] as String),
    );
  }
}

/// Service to manage offline changes and sync them when online
class SyncService with ChangeNotifier {
  static const String _pendingOperationsKey = 'pending_operations';
  final List<PendingOperation> _pendingOperations = [];
  final ConnectivityService _connectivityService;
  final ApiService _apiService;
  late SharedPreferences _prefs;
  bool _isSyncing = false;
  Timer? _syncTimer;

  bool get isSyncing => _isSyncing;
  int get pendingOperationsCount => _pendingOperations.length;
  ConnectivityService get connectivityService => _connectivityService;

  SyncService(this._connectivityService, this._apiService) {
    _init();
  }

  Future<void> _init() async {
    _prefs = await SharedPreferences.getInstance();
    await _loadPendingOperations();

    // Force a connectivity check immediately
    await _connectivityService.checkConnectivity();

    // Listen for connectivity changes
    _connectivityService.addListener(_onConnectivityChanged);

    // Set up periodic sync attempt (every 1 minute)
    _syncTimer = Timer.periodic(
        const Duration(minutes: 1), (_) => syncPendingOperations());

    // Try to sync any pending operations immediately
    if (_connectivityService.isConnected && _pendingOperations.isNotEmpty) {
      debugPrint(
          'Initial sync of ${_pendingOperations.length} pending operations');
      syncPendingOperations();
    }
  }

  Future<void> _loadPendingOperations() async {
    final String? pendingOpsJson = _prefs.getString(_pendingOperationsKey);
    if (pendingOpsJson != null) {
      try {
        final List<dynamic> decoded = jsonDecode(pendingOpsJson);
        _pendingOperations.clear();
        _pendingOperations.addAll(
            decoded.map((json) => PendingOperation.fromJson(json)).toList());
        debugPrint('Loaded ${_pendingOperations.length} pending operations');
      } catch (e) {
        debugPrint('Error loading pending operations: $e');
      }
    }
  }

  Future<void> _savePendingOperations() async {
    final String pendingOpsJson =
        jsonEncode(_pendingOperations.map((op) => op.toJson()).toList());
    await _prefs.setString(_pendingOperationsKey, pendingOpsJson);
  }

  void _onConnectivityChanged() {
    debugPrint(
        'Connectivity changed: ${_connectivityService.isConnected ? 'ONLINE' : 'OFFLINE'}');

    // If we're online, try to sync pending operations
    if (_connectivityService.isConnected) {
      debugPrint(
          'Connection restored, attempting to sync ${_pendingOperations.length} pending operations');

      // Even if there are no pending operations, notify listeners to update UI
      notifyListeners();

      if (_pendingOperations.isNotEmpty) {
        syncPendingOperations();
      }
    } else {
      // We're offline, notify listeners to update UI
      notifyListeners();
    }
  }

  /// Add a pending operation to be synced later
  Future<void> addPendingOperation(
      PendingOperationType type, Map<String, dynamic> data) async {
    _pendingOperations.add(PendingOperation(type: type, data: data));
    await _savePendingOperations();
    notifyListeners();

    // Try to sync immediately if connected
    if (_connectivityService.isConnected) {
      syncPendingOperations();
    }
  }

  /// Sync all pending operations with the server
  Future<void> syncPendingOperations() async {
    if (_isSyncing || _pendingOperations.isEmpty) {
      return;
    }

    // Double-check connectivity
    final isConnected = await _connectivityService.checkConnectivity();
    if (!isConnected) {
      debugPrint('Cannot sync: device is offline');
      return;
    }

    _isSyncing = true;
    notifyListeners();

    debugPrint(
        'Starting sync of ${_pendingOperations.length} pending operations');

    // Sort operations by timestamp
    _pendingOperations.sort((a, b) => a.timestamp.compareTo(b.timestamp));

    // Process operations one by one
    final List<PendingOperation> completedOperations = [];

    for (final operation in _pendingOperations) {
      try {
        debugPrint('Processing operation: ${operation.type}');
        await _processPendingOperation(operation);
        completedOperations.add(operation);
        debugPrint('Operation completed successfully: ${operation.type}');
      } catch (e) {
        debugPrint('Error processing operation: $e');
        // Continue with next operation instead of breaking
        // This way, one bad operation won't block all others
      }
    }

    if (completedOperations.isNotEmpty) {
      debugPrint('Completed ${completedOperations.length} operations');
      // Remove completed operations
      _pendingOperations.removeWhere((op) => completedOperations.contains(op));
      await _savePendingOperations();
    } else {
      debugPrint('No operations were completed successfully');
    }

    _isSyncing = false;
    notifyListeners();
  }

  /// Process a single pending operation
  Future<void> _processPendingOperation(PendingOperation operation) async {
    switch (operation.type) {
      case PendingOperationType.addTodo:
        await _processAddTodo(operation.data);
        break;
      case PendingOperationType.updateTodo:
        await _processUpdateTodo(operation.data);
        break;
      case PendingOperationType.deleteTodo:
        await _processDeleteTodo(operation.data);
        break;
      case PendingOperationType.toggleTodoComplete:
        await _processToggleTodoComplete(operation.data);
        break;
      case PendingOperationType.addCategory:
        await _processAddCategory(operation.data);
        break;
      case PendingOperationType.updateCategory:
        await _processUpdateCategory(operation.data);
        break;
      case PendingOperationType.deleteCategory:
        await _processDeleteCategory(operation.data);
        break;
      case PendingOperationType.addSubtask:
        await _processAddSubtask(operation.data);
        break;
      case PendingOperationType.updateSubtask:
        await _processUpdateSubtask(operation.data);
        break;
      case PendingOperationType.deleteSubtask:
        await _processDeleteSubtask(operation.data);
        break;
      case PendingOperationType.reorderTodos:
        await _processReorderTodos(operation.data);
        break;
    }
  }

  // Implementation of individual operation processors
  Future<void> _processAddTodo(Map<String, dynamic> data) async {
    // Handle subtasks properly - convert List<dynamic> to List<Map<String, dynamic>>
    List<Map<String, dynamic>>? subtasks;
    if (data['subtasks'] != null) {
      try {
        subtasks = (data['subtasks'] as List)
            .map((item) => item is Map<String, dynamic>
                ? item
                : Map<String, dynamic>.from(item as Map))
            .toList();
      } catch (e) {
        debugPrint('Error converting subtasks: $e');
        subtasks = null;
      }
    }

    // Handle category ID - check if we have a category name instead
    int? categoryId;
    String? categoryName = data['categoryName'];

    if (categoryName != null && categoryName.isNotEmpty) {
      // We have a category name, need to create the category first
      debugPrint('Creating category first: $categoryName');
      try {
        final categoryData = await _apiService.addCategory(categoryName);
        categoryId = int.parse(categoryData['id'].toString());
        debugPrint('Created category with ID: $categoryId');
      } catch (e) {
        debugPrint('Failed to create category: $e');
        // If category creation fails, we can't proceed
        throw Exception('Failed to create category "$categoryName": $e');
      }
    } else {
      // Use the provided category ID
      categoryId = data['categoryId'];
      if (categoryId == null || categoryId < 0) {
        debugPrint(
            'Invalid categoryId: $categoryId, cannot proceed without valid category');
        throw Exception('Invalid category ID: $categoryId');
      }
    }

    // Ensure text and priority are not null or empty
    String text = data['text'] ?? '';
    if (text.isEmpty) {
      debugPrint('Empty text, using default value');
      text = 'Untitled task';
    }

    String priority = data['priority'] ?? '';
    if (priority.isEmpty) {
      debugPrint('Empty priority, using default value');
      priority = 'medium';
    }

    try {
      debugPrint(
          'Adding todo with validated data: text=$text, categoryId=$categoryId, priority=$priority');

      await _apiService.addTodo(
        text: text,
        categoryId: categoryId,
        priority: priority,
        dueDate: data['dueDate'],
        subtasks: subtasks,
      );
    } catch (e) {
      debugPrint('Failed to add todo: $e');
      rethrow;
    }
  }

  Future<void> _processUpdateTodo(Map<String, dynamic> data) async {
    // We'll focus on the required fields first and handle subtasks separately if needed
    // Subtasks processing is removed as we're not using it in the initial update

    // We'll let the ApiService handle finding a valid category ID
    // It will fetch the user's categories and use a valid one
    // Extract the categoryId from the data, even if it's invalid
    int? categoryId;
    if (data['categoryId'] != null) {
      if (data['categoryId'] is int) {
        categoryId = data['categoryId'];
      } else if (data['categoryId'] is String) {
        categoryId = int.tryParse(data['categoryId'].toString());
      }
    }

    debugPrint(
        'Extracted categoryId from data: $categoryId (will be validated by ApiService)');

    // Ensure text is not null or empty
    String text = data['text'] ?? '';
    if (text.isEmpty || text.trim().isEmpty) {
      debugPrint('Empty text, using default value');
      text = 'Untitled task';
    }

    // Ensure priority is valid and lowercase (API expects lowercase)
    String priority = data['priority'] ?? '';
    if (priority.isEmpty ||
        !['low', 'medium', 'high'].contains(priority.toLowerCase())) {
      debugPrint('Invalid priority: $priority, using default value');
      priority = 'medium';
    } else {
      priority =
          priority.toLowerCase(); // Ensure lowercase to match API expectations
    }

    // Extract due date if it exists
    String? dueDate = data['dueDate'] as String?;
    if (dueDate != null) {
      debugPrint('Found dueDate in pending operation: $dueDate');
    }

    try {
      // Create a payload with all the necessary fields
      final Map<String, dynamic> payload = {
        'text': text,
        'categoryId': categoryId,
        'priority': priority,
      };

      // Add dueDate if it exists
      if (dueDate != null) {
        payload['dueDate'] = dueDate;
      }

      debugPrint(
          'Updating todo with validated data: id=${data['id']}, text=$text, categoryId=$categoryId, priority=$priority, dueDate=$dueDate');
      debugPrint('Payload: ${jsonEncode(payload)}');

      // Include all fields in the update, including optional ones
      await _apiService.updateTodo(
        id: data['id'],
        text: text,
        categoryId: categoryId,
        priority: priority,
        dueDate: dueDate,
      );
    } catch (e) {
      debugPrint('Update failed with error: $e');

      // If we get an "Invalid category" error, try again with the default category
      if (e.toString().contains('Invalid category')) {
        debugPrint('Retrying with default category (1)');
        await _apiService.updateTodo(
          id: data['id'],
          text: text,
          categoryId: 1, // Use default category (1, not 0)
          priority: priority,
          dueDate: dueDate, // Include the due date
        );
      } else if (e.toString().contains('required')) {
        // If we get a "required fields" error, try with hardcoded minimal required fields
        // Make sure we're sending exactly what the API expects
        debugPrint('Retrying with hardcoded minimal required fields');
        await _apiService.updateTodo(
          id: data['id'],
          text: 'Untitled task',
          categoryId: 1, // Use 1, not 0
          priority: 'medium',
          dueDate: dueDate, // Include the due date
        );
      } else {
        // For other errors, rethrow
        rethrow;
      }
    }
  }

  Future<void> _processDeleteTodo(Map<String, dynamic> data) async {
    await _apiService.deleteTodo(data['id']);
  }

  Future<void> _processToggleTodoComplete(Map<String, dynamic> data) async {
    await _apiService.toggleTodoComplete(data['id']);
  }

  Future<void> _processAddCategory(Map<String, dynamic> data) async {
    await _apiService.addCategory(data['name']);
  }

  Future<void> _processUpdateCategory(Map<String, dynamic> data) async {
    await _apiService.updateCategory(data['categoryId'], data['name']);
  }

  Future<void> _processDeleteCategory(Map<String, dynamic> data) async {
    await _apiService.deleteCategory(data['categoryId']);
  }

  Future<void> _processAddSubtask(Map<String, dynamic> data) async {
    // Extract the todo ID - handle both int and String types
    dynamic todoId = data['todoId'];

    // Extract text
    String text = data['text'] ?? '';

    debugPrint('Processing add subtask: todoId=$todoId, text=$text');

    try {
      await _apiService.addSubtask(todoId, text);
      debugPrint('Subtask added successfully');
    } catch (e) {
      debugPrint('Error processing add subtask: $e');
      rethrow;
    }
  }

  Future<void> _processUpdateSubtask(Map<String, dynamic> data) async {
    // Extract the subtask ID - handle both int and String types
    dynamic subtaskId = data['id'];

    // Extract text and completed status
    String text = data['text'] ?? '';
    bool completed = data['completed'] ?? false;

    debugPrint(
        'Processing update subtask: id=$subtaskId, text=$text, completed=$completed');

    try {
      await _apiService.updateSubtask(
        subtaskId,
        text,
        completed,
      );
      debugPrint('Subtask update processed successfully');
    } catch (e) {
      debugPrint('Error processing subtask update: $e');
      rethrow;
    }
  }

  Future<void> _processDeleteSubtask(Map<String, dynamic> data) async {
    // Implement when API supports this
  }

  Future<void> _processReorderTodos(Map<String, dynamic> data) async {
    await _apiService.reorderTodos(data['order']);
  }

  @override
  void dispose() {
    _connectivityService.removeListener(_onConnectivityChanged);
    _syncTimer?.cancel();
    super.dispose();
  }
}
