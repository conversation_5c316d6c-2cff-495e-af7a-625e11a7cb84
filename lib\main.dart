import 'package:flutter/material.dart';
import 'package:provider/provider.dart' hide ProxyProvider;
import 'package:window_size/window_size.dart';
import 'package:flutter/foundation.dart';
import 'dart:io';
import 'package:shared_preferences/shared_preferences.dart';
import 'core/providers/theme_provider.dart';
import 'core/providers/todo_provider.dart';
import 'core/providers/auth_provider.dart';
import 'core/services/api_service.dart';
import 'core/services/connectivity_service.dart';
import 'core/services/sync_service.dart';
import 'core/services/category_service.dart';
import 'ui/screens/home_screen.dart';
import 'ui/theme/app_theme.dart';

void main() {
  WidgetsFlutterBinding.ensureInitialized();

  // Set minimum window size for desktop
  if (!kIsWeb && (Platform.isWindows || Platform.isLinux || Platform.isMacOS)) {
    setWindowMinSize(const Size(400, 600));
    setWindowMaxSize(Size.infinite);
  }

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        // Core providers and services
        ChangeNotifierProvider(create: (_) => ThemeProvider()),
        Provider<ApiService>(
          create: (_) => ApiService(SharedPreferences.getInstance()),
        ),
        ChangeNotifierProxyProvider<ApiService, AuthProvider>(
          create: (context) => AuthProvider(),
          update: (_, apiService, previous) {
            if (previous != null) {
              previous.updateApiService(apiService);
              return previous;
            }
            return AuthProvider(apiService: apiService);
          },
        ),
        ChangeNotifierProxyProvider<ApiService, ConnectivityService>(
          create: (context) => ConnectivityService(),
          update: (_, apiService, previous) {
            if (previous != null) {
              previous.updateApiService(apiService);
              return previous;
            }
            return ConnectivityService(apiService: apiService);
          },
        ),
        ChangeNotifierProvider<SyncService>(
          create: (context) {
            final connectivityService =
                Provider.of<ConnectivityService>(context, listen: false);
            final apiService = Provider.of<ApiService>(context, listen: false);
            return SyncService(connectivityService, apiService);
          },
        ),
        Provider<CategoryService>(
          create: (_) => CategoryService([]),
        ),

        // TodoProvider depends on multiple services
        ChangeNotifierProxyProvider3<AuthProvider, CategoryService, SyncService,
            TodoProvider>(
          create: (context) => TodoProvider(
            context.read<AuthProvider>(),
            context.read<CategoryService>(),
            context.read<SyncService>(),
            apiService: context.read<ApiService>(),
          ),
          update:
              (context, authProvider, categoryService, syncService, previous) {
            previous?.updateAuthProvider(authProvider);
            return previous ??
                TodoProvider(
                  authProvider,
                  categoryService,
                  syncService,
                  apiService: context.read<ApiService>(),
                );
          },
        ),
      ],
      child: Consumer<ThemeProvider>(
        builder: (context, themeProvider, child) {
          return MaterialApp(
            title: 'Todo App',
            debugShowCheckedModeBanner: false,
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: themeProvider.themeMode,
            home: const HomeScreen(),
          );
        },
      ),
    );
  }
}
