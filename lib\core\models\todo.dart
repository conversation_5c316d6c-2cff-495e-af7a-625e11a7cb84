import 'package:uuid/uuid.dart';
import 'category.dart' as cat;

class Todo {
  final String id;
  String text;
  bool completed;
  String category;
  String priority;
  DateTime? dueDate;
  List<Subtask> subtasks;
  int displayOrder;

  Todo({
    String? id,
    required this.text,
    this.completed = false,
    required this.category,
    this.priority = 'medium',
    this.dueDate,
    List<Subtask>? subtasks,
    this.displayOrder = 0,
  })  : id = id ?? const Uuid().v4(),
        subtasks = subtasks ?? [];

  double get progress {
    if (subtasks.isEmpty) return completed ? 1.0 : 0.0;
    final completedSubtasks = subtasks.where((s) => s.completed).length;
    return completedSubtasks / subtasks.length;
  }

  Todo copyWith({
    String? text,
    bool? completed,
    String? category,
    String? priority,
    DateTime? dueDate,
    List<Subtask>? subtasks,
    int? displayOrder,
  }) {
    return Todo(
      id: id,
      text: text ?? this.text,
      completed: completed ?? this.completed,
      category: category ?? this.category,
      priority: priority ?? this.priority,
      dueDate: dueDate ?? this.dueDate,
      subtasks: subtasks ?? List.from(this.subtasks),
      displayOrder: displayOrder ?? this.displayOrder,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'text': text,
      'completed': completed,
      'category': category,
      'priority': priority,
      'dueDate': dueDate?.toIso8601String(),
      'subtasks': subtasks.map((s) => s.toJson()).toList(),
      'displayOrder': displayOrder,
    };
  }

  factory Todo.fromJson(Map<String, dynamic> json,
      {List<cat.Category>? categories}) {
    String categoryName = 'Uncategorized';
    if (categories != null && json['category_id'] != null) {
      final catObj = categories.firstWhere(
        (c) => c.id == json['category_id'].toString(),
        orElse: () =>
            cat.Category(id: '', name: 'Uncategorized', color: '#000000'),
      );
      categoryName = catObj.name;
    } else if (json['categoryName'] != null) {
      categoryName = json['categoryName'] as String;
    } else if (json['category'] != null) {
      categoryName = json['category'] as String;
    }

    // Handle subtasks from API or local storage
    List<Subtask> subtasksList = [];
    if (json['subtasks'] != null) {
      subtasksList = (json['subtasks'] as List)
          .map((s) => Subtask.fromJson(s as Map<String, dynamic>))
          .toList();
    }

    return Todo(
      id: json['id']?.toString() ?? const Uuid().v4(),
      text: json['text'] as String? ?? '',
      completed: json['completed'] as bool? ?? false,
      category: categoryName,
      priority: json['priority'] as String? ?? 'medium',
      dueDate: json['due_date'] != null
          ? DateTime.parse(json['due_date'])
          : (json['dueDate'] != null ? DateTime.parse(json['dueDate']) : null),
      subtasks: subtasksList,
      displayOrder:
          json['display_order'] as int? ?? json['displayOrder'] as int? ?? 0,
    );
  }
}

class Subtask {
  final String id;
  String text;
  bool completed;

  Subtask({
    String? id,
    required this.text,
    this.completed = false,
  }) : id = id ?? const Uuid().v4();

  Subtask copyWith({
    String? text,
    bool? completed,
  }) {
    return Subtask(
      id: id,
      text: text ?? this.text,
      completed: completed ?? this.completed,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'text': text,
      'completed': completed,
    };
  }

  factory Subtask.fromJson(Map<String, dynamic> json) {
    return Subtask(
      id: json['id']?.toString() ?? const Uuid().v4(),
      text: json['text'] as String? ?? '',
      completed: json['completed'] as bool? ?? false,
    );
  }
}
