import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

class ApiService {
  static const String baseUrl = 'https://iqtp.tplinkdns.com/api/todo';
  final Future<SharedPreferences> _prefsFuture;
  SharedPreferences? _prefs;

  ApiService(this._prefsFuture) {
    _initPrefs();
  }

  Future<void> _initPrefs() async {
    _prefs = await _prefsFuture;
  }

  Future<SharedPreferences> get _getPrefs async {
    if (_prefs != null) return _prefs!;
    _prefs = await _prefsFuture;
    return _prefs!;
  }

  Map<String, String> get _headers => {
        'Content-Type': 'application/json',
        if (_prefs?.getString('todo_token') != null)
          'Authorization': 'Bearer ${_prefs!.getString('todo_token')}',
      };

  /// Check if the API is reachable
  Future<bool> ping() async {
    try {
      // Try to load todos as a more reliable check
      try {
        if (_prefs?.getString('todo_token') != null) {
          // Only try this if we have a token
          final response = await http
              .get(
                Uri.parse('$baseUrl/load'),
                headers: _headers,
              )
              .timeout(const Duration(seconds: 3));

          debugPrint('Load todos response: ${response.statusCode}');
          if (response.statusCode >= 200 && response.statusCode < 300) {
            return true;
          }
        }
      } catch (e) {
        debugPrint('Load todos failed: $e');
      }

      // Try a simple GET request to the server
      final response = await http.get(
        Uri.parse(baseUrl),
        headers: {
          'Content-Type': 'application/json'
        }, // Use simple headers to avoid auth issues
      ).timeout(const Duration(seconds: 3));

      debugPrint('Ping response: ${response.statusCode}');

      // For the API server, we should get a 200 response
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return true;
      }

      // If we get here, try a well-known site as a fallback
      final fallbackResponse = await http
          .get(
            Uri.parse('https://www.google.com'),
          )
          .timeout(const Duration(seconds: 3));

      debugPrint('Fallback ping response: ${fallbackResponse.statusCode}');
      return fallbackResponse.statusCode >= 200 &&
          fallbackResponse.statusCode < 300;
    } catch (e) {
      debugPrint('All ping attempts failed: $e');
      return false;
    }
  }

  // Auth APIs
  Future<Map<String, dynamic>> login(String email, String password) async {
    final response = await http.post(
      Uri.parse('$baseUrl/login'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({'email': email, 'password': password}),
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      final prefs = await _getPrefs;
      await prefs.setString('todo_token', data['token']);
      await prefs.setString(
          'todo_user_name', data['name'] ?? email.split('@')[0]);
      return data;
    } else {
      throw Exception(jsonDecode(response.body)['error'] ?? 'Login failed');
    }
  }

  Future<Map<String, dynamic>> register(
      String email, String password, String? name) async {
    final response = await http.post(
      Uri.parse('$baseUrl/register'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        'email': email,
        'password': password,
        if (name != null) 'name': name,
      }),
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      final prefs = await _getPrefs;
      await prefs.setString('todo_token', data['token']);
      await prefs.setString(
          'todo_user_name', data['name'] ?? email.split('@')[0]);
      return data;
    } else {
      throw Exception(
          jsonDecode(response.body)['error'] ?? 'Registration failed');
    }
  }

  Future<void> forgotPassword(String email) async {
    final response = await http.post(
      Uri.parse('$baseUrl/forgot-password'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({'email': email}),
    );

    if (response.statusCode != 200) {
      throw Exception(
          jsonDecode(response.body)['error'] ?? 'Failed to send reset link');
    }
  }

  // Todo APIs
  Future<Map<String, dynamic>> loadTodos() async {
    final response = await http.get(
      Uri.parse('$baseUrl/load'),
      headers: _headers,
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);

      // Ensure the response has the expected structure
      if (data is! Map<String, dynamic>) {
        throw Exception('Invalid response format');
      }

      // Ensure categories and tasks are properly formatted
      if (data['categories'] != null && data['categories'] is! List) {
        data['categories'] = [];
      }

      if (data['tasks'] != null && data['tasks'] is! List) {
        data['tasks'] = [];
      }

      return data;
    } else {
      throw Exception(
          jsonDecode(response.body)['error'] ?? 'Failed to load todos');
    }
  }

  Future<Map<String, dynamic>> addTodo({
    required String text,
    required int categoryId,
    required String priority,
    String? dueDate,
    List<Map<String, dynamic>>? subtasks,
  }) async {
    final Map<String, dynamic> payload = {
      'text': text,
      'categoryId': categoryId,
      'priority': priority,
      if (dueDate != null) 'dueDate': dueDate,
      if (subtasks != null) 'subtasks': subtasks,
    };

    final response = await http.post(
      Uri.parse('$baseUrl/tasks'),
      headers: _headers,
      body: jsonEncode(payload),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception(
          jsonDecode(response.body)['error'] ?? 'Failed to add todo');
    }
  }

  Future<Map<String, dynamic>> updateTodo({
    required int id,
    required String text,
    required int? categoryId,
    required String priority,
    String? dueDate,
    List<Map<String, dynamic>>? subtasks,
  }) async {
    // Validate required fields
    if (text.isEmpty) {
      text = 'Untitled task';
      debugPrint('Empty text provided, using default: $text');
    }

    // CRITICAL: We need to ensure we're using a valid category ID that belongs to the user
    int nonNullCategoryId;

    // Always try to get the user's categories first, regardless of the provided categoryId
    debugPrint('Fetching user categories to ensure we use a valid one');
    try {
      // Try to get the user's categories from the server
      final response = await http.get(
        Uri.parse('$baseUrl/load'),
        headers: _headers,
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['categories'] != null && data['categories'].isNotEmpty) {
          // Check if the provided categoryId is valid for this user
          if (categoryId != null && categoryId > 0) {
            final userCategories = data['categories'] as List;
            final categoryExists =
                userCategories.any((cat) => cat['id'] == categoryId);

            if (categoryExists) {
              // Use the provided categoryId since it's valid
              nonNullCategoryId = categoryId;
              debugPrint(
                  'Using provided categoryId: $nonNullCategoryId (verified as valid)');
            } else {
              // Use the first available category since the provided one isn't valid
              nonNullCategoryId = data['categories'][0]['id'];
              debugPrint(
                  'Provided categoryId $categoryId is not valid for this user, using: $nonNullCategoryId');
            }
          } else {
            // No valid categoryId provided, use the first available one
            nonNullCategoryId = data['categories'][0]['id'];
            debugPrint(
                'No valid categoryId provided, using first available: $nonNullCategoryId');
          }
        } else {
          // No categories exist, create a default one
          debugPrint('No categories found for user, creating a default one');
          final createCatResponse = await http.post(
            Uri.parse('$baseUrl/categories'),
            headers: {..._headers, 'Content-Type': 'application/json'},
            body: jsonEncode({'name': 'General'}),
          );

          if (createCatResponse.statusCode == 201) {
            final catData = jsonDecode(createCatResponse.body);
            nonNullCategoryId = catData['id'];
            debugPrint('Created default category with ID: $nonNullCategoryId');
          } else {
            // If we can't create a category, we can't proceed
            throw Exception(
                'Failed to create a category and no existing categories found');
          }
        }
      } else {
        // If we can't get categories, we can't proceed
        throw Exception(
            'Failed to fetch user categories: ${response.statusCode}');
      }
    } catch (e) {
      // If any error occurs, we need to handle it gracefully
      debugPrint('Error getting valid category: $e');

      // Try creating a category as a last resort
      try {
        debugPrint('Attempting to create a default category as fallback');
        final createCatResponse = await http.post(
          Uri.parse('$baseUrl/categories'),
          headers: {..._headers, 'Content-Type': 'application/json'},
          body: jsonEncode({'name': 'General'}),
        );

        if (createCatResponse.statusCode == 201) {
          final catData = jsonDecode(createCatResponse.body);
          nonNullCategoryId = catData['id'];
          debugPrint('Created fallback category with ID: $nonNullCategoryId');
        } else {
          // If we can't create a category, we can't proceed
          throw Exception('Failed to create a fallback category');
        }
      } catch (e2) {
        // If we can't create a category, we can't proceed
        throw Exception('Unable to find or create a valid category: $e2');
      }
    }

    // Ensure priority is valid
    if (!['low', 'medium', 'high'].contains(priority.toLowerCase())) {
      priority = 'medium';
      debugPrint('Invalid priority provided, using default: $priority');
    }

    // Create a minimal payload with EXACTLY the required fields in the format the API expects
    final Map<String, dynamic> payload = {
      'text': text,
      'categoryId': nonNullCategoryId,
      'priority':
          priority.toLowerCase(), // Ensure lowercase to match API expectations
    };

    // Only add optional fields if they're actually needed by the API
    // Based on the web app implementation, these are optional
    if (dueDate != null && dueDate.isNotEmpty) {
      payload['dueDate'] = dueDate;
    }

    // Subtasks are handled separately in the web app, so we'll exclude them here
    // to avoid potential format issues

    // Log detailed payload information
    debugPrint('Updating todo $id with payload: ${jsonEncode(payload)}');
    debugPrint('Payload details:');
    debugPrint('- text: "${payload['text']}" (${payload['text'].runtimeType})');
    debugPrint(
        '- categoryId: ${payload['categoryId']} (${payload['categoryId'].runtimeType})');
    debugPrint(
        '- priority: "${payload['priority']}" (${payload['priority'].runtimeType})');

    try {
      // Ensure headers are properly set
      final Map<String, String> headers = {
        ..._headers,
        'Content-Type': 'application/json',
      };

      final response = await http.put(
        Uri.parse('$baseUrl/tasks/$id'),
        headers: headers,
        body: jsonEncode(payload),
      );

      if (response.statusCode == 200) {
        debugPrint('Todo update successful. Response: ${response.body}');
        return jsonDecode(response.body);
      } else {
        // Try to parse the error response
        String errorMessage = 'Failed to update todo';
        try {
          final errorData = jsonDecode(response.body);
          if (errorData != null && errorData['error'] != null) {
            errorMessage = errorData['error'];
          }
        } catch (e) {
          debugPrint('Error parsing error response: $e');
        }

        debugPrint(
            'Todo update failed. Status: ${response.statusCode}, Body: ${response.body}');
        debugPrint('Error message: $errorMessage');

        throw Exception(errorMessage);
      }
    } catch (e) {
      debugPrint('Exception during todo update: $e');
      rethrow;
    }
  }

  Future<void> deleteTodo(int id) async {
    final response = await http.delete(
      Uri.parse('$baseUrl/tasks/$id'),
      headers: _headers,
    );

    if (response.statusCode != 200) {
      throw Exception(
          jsonDecode(response.body)['error'] ?? 'Failed to delete todo');
    }
  }

  Future<void> toggleTodoComplete(int id) async {
    final response = await http.patch(
      Uri.parse('$baseUrl/tasks/$id/toggle'),
      headers: _headers,
    );

    if (response.statusCode != 200) {
      throw Exception(
          jsonDecode(response.body)['error'] ?? 'Failed to toggle todo');
    }
  }

  Future<Map<String, dynamic>> updateTodoTitle(int id, String title,
      {required String priority,
      required int categoryId,
      String? dueDate}) async {
    debugPrint('Updating todo title for id $id to: $title');

    try {
      // Prepare the payload with all required fields from the local todo data
      // CRITICAL: We need to ensure we're using a valid category ID that belongs to the user
      int validCategoryId;

      // Always try to get the user's categories first, regardless of the provided categoryId
      debugPrint('Fetching user categories to ensure we use a valid one');
      try {
        // Try to get the user's categories from the server
        final response = await http.get(
          Uri.parse('$baseUrl/load'),
          headers: _headers,
        );

        if (response.statusCode == 200) {
          final data = jsonDecode(response.body);
          if (data['categories'] != null && data['categories'].isNotEmpty) {
            // Check if the provided categoryId is valid for this user
            if (categoryId > 0) {
              final userCategories = data['categories'] as List;
              final categoryExists =
                  userCategories.any((cat) => cat['id'] == categoryId);

              if (categoryExists) {
                // Use the provided categoryId since it's valid
                validCategoryId = categoryId;
                debugPrint(
                    'Using provided categoryId: $validCategoryId (verified as valid)');
              } else {
                // Use the first available category since the provided one isn't valid
                validCategoryId = data['categories'][0]['id'];
                debugPrint(
                    'Provided categoryId $categoryId is not valid for this user, using: $validCategoryId');
              }
            } else {
              // No valid categoryId provided, use the first available one
              validCategoryId = data['categories'][0]['id'];
              debugPrint(
                  'No valid categoryId provided, using first available: $validCategoryId');
            }
          } else {
            // No categories exist, create a default one
            debugPrint('No categories found for user, creating a default one');
            final createCatResponse = await http.post(
              Uri.parse('$baseUrl/categories'),
              headers: {..._headers, 'Content-Type': 'application/json'},
              body: jsonEncode({'name': 'General'}),
            );

            if (createCatResponse.statusCode == 201) {
              final catData = jsonDecode(createCatResponse.body);
              validCategoryId = catData['id'];
              debugPrint('Created default category with ID: $validCategoryId');
            } else {
              // If we can't create a category, we can't proceed
              throw Exception(
                  'Failed to create a category and no existing categories found');
            }
          }
        } else {
          // If we can't get categories, we can't proceed
          throw Exception(
              'Failed to fetch user categories: ${response.statusCode}');
        }
      } catch (e) {
        // If any error occurs, we need to handle it gracefully
        debugPrint('Error getting valid category: $e');

        // Try creating a category as a last resort
        try {
          debugPrint('Attempting to create a default category as fallback');
          final createCatResponse = await http.post(
            Uri.parse('$baseUrl/categories'),
            headers: {..._headers, 'Content-Type': 'application/json'},
            body: jsonEncode({'name': 'General'}),
          );

          if (createCatResponse.statusCode == 201) {
            final catData = jsonDecode(createCatResponse.body);
            validCategoryId = catData['id'];
            debugPrint('Created fallback category with ID: $validCategoryId');
          } else {
            // If we can't create a category, we can't proceed
            throw Exception('Failed to create a fallback category');
          }
        } catch (e2) {
          // If we can't create a category, we can't proceed
          throw Exception('Unable to find or create a valid category: $e2');
        }
      }

      // Make sure priority is valid and lowercase
      String validPriority =
          priority.isNotEmpty ? priority.toLowerCase() : 'medium';
      if (!['low', 'medium', 'high'].contains(validPriority)) {
        validPriority = 'medium';
      }

      final Map<String, dynamic> payload = {
        'text': title,
        'categoryId': validCategoryId, // Send as integer
        'priority': validPriority,
      };

      // Add optional fields if provided
      if (dueDate != null && dueDate.isNotEmpty) {
        payload['dueDate'] = dueDate;
      }

      // Debug the payload in detail
      debugPrint('Payload details:');
      debugPrint('- text: ${payload['text']} (${payload['text'].runtimeType})');
      debugPrint(
          '- categoryId: ${payload['categoryId']} (${payload['categoryId'].runtimeType})');
      debugPrint(
          '- priority: ${payload['priority']} (${payload['priority'].runtimeType})');

      debugPrint('Updating todo with payload: ${jsonEncode(payload)}');

      // Update the todo with all required fields
      try {
        final response = await http.put(
          Uri.parse('$baseUrl/tasks/$id'),
          headers: _headers,
          body: jsonEncode(payload),
        );

        if (response.statusCode == 200) {
          debugPrint(
              'Todo title update successful. Response: ${response.body}');
          return jsonDecode(response.body);
        } else {
          // Try to parse the error response
          String errorMessage = 'Failed to update todo title';
          try {
            final errorData = jsonDecode(response.body);
            if (errorData != null && errorData['error'] != null) {
              errorMessage = errorData['error'];
            }
          } catch (e) {
            debugPrint('Error parsing error response: $e');
          }

          debugPrint(
              'Todo title update failed. Status: ${response.statusCode}, Body: ${response.body}');
          debugPrint('Error message: $errorMessage');

          throw Exception(errorMessage);
        }
      } catch (e) {
        if (e is Exception) {
          rethrow;
        }
        throw Exception('Network error: ${e.toString()}');
      }
    } catch (e) {
      debugPrint('Error in updateTodoTitle: $e');
      rethrow;
    }
  }

  Future<void> reorderTodos(List<Map<String, dynamic>> order) async {
    final response = await http.post(
      Uri.parse('$baseUrl/tasks/reorder'),
      headers: _headers,
      body: jsonEncode({'order': order}),
    );

    if (response.statusCode != 200) {
      throw Exception(
          jsonDecode(response.body)['error'] ?? 'Failed to reorder todos');
    }
  }

  // Category APIs
  Future<Map<String, dynamic>> addCategory(String name) async {
    final response = await http.post(
      Uri.parse('$baseUrl/categories'),
      headers: _headers,
      body: jsonEncode({'name': name}),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception(
          jsonDecode(response.body)['error'] ?? 'Failed to add category');
    }
  }

  Future<void> updateCategory(int id, String name) async {
    final response = await http.put(
      Uri.parse('$baseUrl/categories/$id'),
      headers: _headers,
      body: jsonEncode({'name': name}),
    );

    if (response.statusCode != 200) {
      throw Exception(
          jsonDecode(response.body)['error'] ?? 'Failed to update category');
    }
  }

  Future<void> deleteCategory(int id) async {
    final response = await http.delete(
      Uri.parse('$baseUrl/categories/$id'),
      headers: _headers,
    );

    if (response.statusCode != 200) {
      throw Exception(
          jsonDecode(response.body)['error'] ?? 'Failed to delete category');
    }
  }

  // Subtask APIs
  Future<Map<String, dynamic>> addSubtask(dynamic todoId, String text) async {
    // Handle both integer and string IDs
    // The backend expects an integer ID, but our local IDs might be UUIDs
    String taskId;

    if (todoId is int) {
      taskId = todoId.toString();
    } else if (todoId is String) {
      // If it's a UUID, we need to convert it to an integer if possible
      try {
        // Try to parse it as an integer first
        int.parse(todoId);
        taskId = todoId;
      } catch (e) {
        // If it's a UUID, we need to handle it differently
        debugPrint(
            'Warning: todoId is a UUID string that cannot be parsed as an integer: $todoId');
        taskId = todoId;
      }
    } else {
      throw Exception('Invalid todo ID type: ${todoId.runtimeType}');
    }

    debugPrint('Adding subtask to todo $taskId with text: "$text"');

    try {
      final response = await http.post(
        Uri.parse('$baseUrl/tasks/$taskId/subtasks'),
        headers: {..._headers, 'Content-Type': 'application/json'},
        body: jsonEncode({'text': text}),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final result = jsonDecode(response.body);
        debugPrint('Subtask added successfully: ${result['id']}');
        return result;
      } else {
        final errorMessage =
            jsonDecode(response.body)['error'] ?? 'Failed to add subtask';
        debugPrint(
            'Failed to add subtask. Status: ${response.statusCode}, Error: $errorMessage');
        throw Exception(errorMessage);
      }
    } catch (e) {
      debugPrint('Exception during add subtask: $e');
      rethrow;
    }
  }

  Future<void> updateSubtask(dynamic id, String text, bool completed) async {
    // Handle both integer and string IDs
    // The backend expects an integer ID, but our local IDs might be UUIDs
    String subtaskId;

    if (id is int) {
      subtaskId = id.toString();
    } else if (id is String) {
      // If it's a UUID, we need to handle it differently
      // For now, we'll just use it as is and let the backend handle it
      subtaskId = id;
    } else {
      throw Exception('Invalid subtask ID type: ${id.runtimeType}');
    }

    debugPrint(
        'Updating subtask $subtaskId with text: "$text", completed: $completed');

    try {
      final response = await http.put(
        Uri.parse('$baseUrl/subtasks/$subtaskId'),
        headers: {..._headers, 'Content-Type': 'application/json'},
        body: jsonEncode({
          'text': text,
          'completed': completed,
        }),
      );

      if (response.statusCode == 200) {
        debugPrint('Subtask update successful');
      } else {
        final errorMessage =
            jsonDecode(response.body)['error'] ?? 'Failed to update subtask';
        debugPrint(
            'Subtask update failed. Status: ${response.statusCode}, Error: $errorMessage');
        throw Exception(errorMessage);
      }
    } catch (e) {
      debugPrint('Exception during subtask update: $e');
      rethrow;
    }
  }

  Future<void> deleteSubtask(dynamic id) async {
    // Handle both integer and string IDs
    // The backend expects an integer ID, but our local IDs might be UUIDs
    String subtaskId;

    if (id is int) {
      subtaskId = id.toString();
    } else if (id is String) {
      // If it's a UUID, we need to handle it differently
      subtaskId = id;
    } else {
      throw Exception('Invalid subtask ID type: ${id.runtimeType}');
    }

    debugPrint('Deleting subtask $subtaskId');

    try {
      final response = await http.delete(
        Uri.parse('$baseUrl/subtasks/$subtaskId'),
        headers: _headers,
      );

      if (response.statusCode == 200) {
        debugPrint('Subtask deleted successfully');
      } else {
        final errorMessage =
            jsonDecode(response.body)['error'] ?? 'Failed to delete subtask';
        debugPrint(
            'Failed to delete subtask. Status: ${response.statusCode}, Error: $errorMessage');
        throw Exception(errorMessage);
      }
    } catch (e) {
      debugPrint('Exception during delete subtask: $e');
      rethrow;
    }
  }
}
