import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/providers/todo_provider.dart';
import '../../core/services/sync_service.dart';
import '../../core/services/connectivity_service.dart';

/// A button that allows the user to manually trigger a sync
class SyncButton extends StatelessWidget {
  const SyncButton({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final todoProvider = Provider.of<TodoProvider>(context);
    final syncService = Provider.of<SyncService>(context);
    final connectivityService = Provider.of<ConnectivityService>(context);

    // If not authenticated or no pending operations, don't show anything
    if (!todoProvider.isAuthenticated ||
        syncService.pendingOperationsCount == 0) {
      return const SizedBox.shrink();
    }

    // If already syncing, show a progress indicator
    if (syncService.isSyncing) {
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8.0),
        child: SizedBox(
          width: 24,
          height: 24,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(
              Theme.of(context).colorScheme.primary,
            ),
          ),
        ),
      );
    }

    // If offline, show a button to retry connection
    if (!connectivityService.isConnected) {
      return Tooltip(
        message: 'You appear to be offline. Tap to check connection and retry.',
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8.0),
          child: IconButton(
            icon: const Icon(Icons.sync_problem),
            onPressed: () async {
              // Force a connectivity check
              await connectivityService.checkConnectivity();
              if (connectivityService.isConnected) {
                await todoProvider.syncPendingChanges();
              }
            },
            color: Colors.orange,
          ),
        ),
      );
    }

    // If online with pending operations, show an active sync button
    return Tooltip(
      message: 'Sync ${syncService.pendingOperationsCount} pending changes',
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8.0),
        child: IconButton(
          icon: const Icon(Icons.sync),
          onPressed: () async {
            await todoProvider.syncPendingChanges();
          },
          color: Theme.of(context).colorScheme.primary,
        ),
      ),
    );
  }
}
