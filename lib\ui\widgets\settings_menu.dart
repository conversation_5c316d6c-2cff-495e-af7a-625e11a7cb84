import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/providers/theme_provider.dart';
import '../../core/providers/todo_provider.dart';
import '../../core/models/category.dart' as cat;

class SettingsMenu extends StatelessWidget {
  const SettingsMenu({super.key});

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final todoProvider = Provider.of<TodoProvider>(context);

    return PopupMenuButton(
      icon: Icon(
        Icons.more_vert,
        color: Theme.of(context).colorScheme.primary,
      ),
      itemBuilder: (context) => [
        PopupMenuItem(
          onTap: themeProvider.toggleTheme,
          child: ListTile(
            leading: Icon(
              themeProvider.isDarkMode ? Icons.dark_mode : Icons.light_mode,
            ),
            title: Text(
              themeProvider.isDarkMode ? 'Light Mode' : 'Dark Mode',
            ),
            contentPadding: EdgeInsets.zero,
          ),
        ),
        PopupMenuItem(
          child: const ListTile(
            leading: Icon(Icons.done_all),
            title: Text('Mark All Complete'),
            contentPadding: EdgeInsets.zero,
          ),
          onTap: () {
            todoProvider.markAllComplete();
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('All tasks marked as complete')),
            );
          },
        ),
        PopupMenuItem(
          child: const ListTile(
            leading: Icon(Icons.category),
            title: Text('Manage Categories'),
            contentPadding: EdgeInsets.zero,
          ),
          onTap: () => _showManageCategoriesDialog(context),
        ),
      ],
    );
  }

  Future<void> _showManageCategoriesDialog(BuildContext context) async {
    final todoProvider = Provider.of<TodoProvider>(context, listen: false);
    final categories = List<cat.Category>.from(todoProvider.categories);

    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Manage Categories'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: categories.length,
            itemBuilder: (context, index) {
              final category = categories[index];
              return ListTile(
                title: Text(category.name),
                trailing: IconButton(
                  icon: const Icon(Icons.delete),
                  onPressed: () {
                    todoProvider.deleteCategory(category.name);
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Category "${category.name}" deleted'),
                        action: SnackBarAction(
                          label: 'Undo',
                          onPressed: () {
                            todoProvider.addCategory(category.name);
                          },
                        ),
                      ),
                    );
                  },
                ),
                onTap: () => _showEditCategoryDialog(context, category),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Future<void> _showEditCategoryDialog(
    BuildContext context,
    cat.Category category,
  ) async {
    final controller = TextEditingController(text: category.name);
    final formKey = GlobalKey<FormState>();

    final newCategory = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Category'),
        content: Form(
          key: formKey,
          child: TextFormField(
            controller: controller,
            decoration: const InputDecoration(
              labelText: 'Category Name',
              hintText: 'Enter category name',
            ),
            textCapitalization: TextCapitalization.words,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Please enter a category name';
              }
              return null;
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              if (formKey.currentState!.validate()) {
                Navigator.pop(context, controller.text.trim());
              }
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );

    if (newCategory != null && context.mounted) {
      Provider.of<TodoProvider>(context, listen: false)
          .updateCategory(category.name, newCategory);

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Category renamed to "$newCategory"'),
          ),
        );
      }
    }
  }
}
