import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'api_service.dart';

/// Service to detect and monitor network connectivity
class ConnectivityService with ChangeNotifier {
  bool _isConnected = true;
  Timer? _connectivityCheckTimer;
  static const String _lastConnectedKey = 'last_connected_time';

  bool get isConnected => _isConnected;
  late SharedPreferences _prefs;
  ApiService _apiService;

  ConnectivityService({ApiService? apiService})
      : _apiService =
            apiService ?? ApiService(SharedPreferences.getInstance()) {
    _init();
  }

  void updateApiService(ApiService apiService) {
    _apiService = apiService;
  }

  Future<void> _init() async {
    _prefs = await SharedPreferences.getInstance();

    // Check connectivity immediately
    _checkConnectivity();

    // Set up periodic connectivity check (every 30 seconds)
    _connectivityCheckTimer = Timer.periodic(
        const Duration(seconds: 30), (_) => _checkConnectivity());
  }

  Future<void> _checkConnectivity() async {
    try {
      // First, try to ping the server
      final bool isReachable = await _apiService.ping();

      // If ping fails, try to load todos as a fallback
      if (!isReachable) {
        try {
          await _apiService.loadTodos();
          // If we get here, we're connected
          debugPrint('Ping failed but loadTodos succeeded - we are online');
          _updateConnectivityState(true);
          return;
        } catch (e) {
          // Both ping and loadTodos failed, we're offline
          debugPrint('Both ping and loadTodos failed - we are offline: $e');
          _updateConnectivityState(false);
          return;
        }
      }

      // Ping succeeded
      debugPrint('Ping succeeded - we are online');
      _updateConnectivityState(true);
    } catch (e) {
      debugPrint('Error checking connectivity: $e');
      _updateConnectivityState(false);
    }
  }

  void _updateConnectivityState(bool isConnected) {
    final bool wasConnected = _isConnected;
    _isConnected = isConnected;

    if (_isConnected) {
      // Store the last time we were successfully connected
      _prefs.setString(_lastConnectedKey, DateTime.now().toIso8601String());
    }

    // Only notify if state changed
    if (wasConnected != _isConnected) {
      debugPrint(
          'Connectivity changed: ${_isConnected ? 'ONLINE' : 'OFFLINE'}');
      notifyListeners();
    }
  }

  /// Force a connectivity check
  Future<bool> checkConnectivity() async {
    await _checkConnectivity();
    return _isConnected;
  }

  /// Get the last time the device was connected
  DateTime? getLastConnectedTime() {
    final String? lastConnected = _prefs.getString(_lastConnectedKey);
    if (lastConnected != null) {
      try {
        return DateTime.parse(lastConnected);
      } catch (e) {
        debugPrint('Error parsing last connected time: $e');
      }
    }
    return null;
  }

  @override
  void dispose() {
    _connectivityCheckTimer?.cancel();
    super.dispose();
  }
}
